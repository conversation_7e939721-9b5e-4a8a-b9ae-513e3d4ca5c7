use std::sync::Arc;

use alloy::{network::TransactionBuilder, primitives::{aliases::{U112, U24, U88}, utils::format_units, Address, U256}, providers::{Provider, WalletProvider}, rpc::types::{Log, TransactionReceipt, TransactionRequest}, sol_types::SolCall};
use colored::Colorize;
use futures::{stream::FuturesUnordered, StreamExt};
use futures::stream::{iter, Stream, StreamExt as _};
use itertools::Either;
use std::pin::Pin;
use tokio::sync::mpsc::Receiver;

use crate::{ executor::gas::{AmountAndUsd, Gas, GasTrait}, tools::now_str, vira::{consts, contract::{IVira, ViraData}, errors::DEXError, pool::DexPool, status::{cache::CacheArbitrage, display_util, mev::{self, Mev, Order}}, token::Token, util, Vira}, CONFIG, STATUS, USER, VIRA};
//use eyre::{Ok, Result};

#[derive(Debug, Clone)]
pub struct TokenUnit {
    pub bn : U256,
    pub num : f32,
}

#[derive(Debug, Clone)]
pub struct FindPathResult {
    pub mev : Mev,
    pub rewards : AmountCalculationResult,
    pub order : Order,
}

/// 计算结果结构体
#[derive(Debug, Clone)]
pub struct AmountCalculationResult {
    // 每一次amountOut的金额
    pub amounts: Vec<U256>,
    //套利token获得的数量和对应的usd价值
    pub profit : AmountAndUsd,
    //套利的gas消耗对应token的数量和对应的usd价值
    pub cost : AmountAndUsd,
}

#[derive(Default)]
pub struct Trash {
    pub operators : Vec<Address>,

    pub cost : f32,
    pub profit : f32,
}

impl Trash {
    pub fn new(operators : Vec<Address>) -> Trash {
        Trash {
            operators,
            ..Default::default()
        }
    }

    pub async fn listen(&self, mut updated_pools_stream: Pin<Box<dyn Stream<Item = Vec<Address>> + Send>>, mut sync_complete_rx: Receiver<()>) -> Result<(), DEXError> {
        println!("{} {}", now_str(), "Trash listening...".green());
        let mut sync_completed = false;
        
        // 创建一个FuturesUnordered来管理并发的get_logs请求
        let mut update_futures = FuturesUnordered::new();

        loop {
            tokio::select! {
                // 处理AMM更新流（优先处理）
                updated_pools = updated_pools_stream.next() => {
                    match updated_pools {
                        Some(pools) => {
                            update_futures.push(async move {
                                self.on_updated_pools(pools, sync_completed).await;
                                ()
                            });
                        }
                        None => {
                            println!("{} {}", now_str(), "Trash stream closed, stopping...".red());
                            break;
                        }
                    }
                }
                _ = update_futures.next(), if !update_futures.is_empty() => {
                    // 任务已经在内部处理完毕，这里不需要额外操作
                }
                // 处理同步完成通知（只在未完成时监听）
                _ = sync_complete_rx.recv(), if !sync_completed => {
                    println!("{} {}", now_str(), "Reserve sync completed, continuing to process updates...".green());
                    sync_completed = true;
                }
                // 如果两个流都关闭了，则退出循环
                else => {
                    println!("{} {}", now_str(), "All updated_pools_stream closed, stopping...".red());
                    break;
                }
            }
        }
        
        Ok(())
    }

    async fn on_updated_pools(&self, updated_pools : Vec<Address>, begin_mev : bool) {
        let mut cache = CacheArbitrage::new();
        let mut mevs = vec![];

        let pools = &VIRA.get().unwrap().sm.pools;
        for (i, addr) in updated_pools.iter().enumerate() {
            //打印有变动的POOL
            let p = pools.data.get(addr).unwrap();
            let block = STATUS.latest_block();
            let mut str = format!("{} {} {}, ", now_str(), block, p.addr());
            p.data().tokens.iter().for_each(|t| {
                let reserve = format_units(t.reserve, t.decimal).unwrap();
                let reserve = format!("{:.2}", reserve.parse::<f64>().unwrap());
                str += format!("{} ({}) ", t.symbol, reserve).as_ref();
            });
            println!("{}", str.black());
            if i > 6 {
                println!("       ......");
                break;
            }

            if begin_mev {
                if let Some(mev) = self.find_path(*addr, Order::Asc, &mut cache, None){
                    mevs.extend(mev);
                }
                if let Some(mev) = self.find_path(*addr, Order::Desc, &mut cache, None){
                    mevs.extend(mev);
                }
            }
        }

        if mevs.len() > 0 {
            println!("{} {} {}", now_str(), "find mev: ".green() , &mevs.len().to_string());
            display_util::display_find_path_result(&mevs);
            self.send_mevs(mevs).await.expect("send mev error");
        }
    }

    

    /// 寻找套利路径的主函数
    ///
    /// 优化特点：
    /// 1. 返回 None 当无法找到有效的 MEV 套利路径时
    /// 2. 返回 None 当找到的路径没有利润时
    /// 3. 使用黄金分割法进行 optimal_amount 计算优化
    /// 4. 实现懒加载缓存策略，减少内存分配
    #[inline(always)]
    fn find_path(
        &self,
        pair_addr: Address,
        order: Order,
        cache: &mut CacheArbitrage,
        path_num: Option<usize>
    ) -> Option<Vec<FindPathResult>> {
        let pools = &VIRA.get().unwrap().sm.pools;
        // 获取MEV路径，如果没有则返回None
        let mev_paths = match pools.mevs.get(&pair_addr) {
            Some(paths) => paths,
            None => return None, // 没有MEV路径直接返回None
        };

        if mev_paths.is_empty() {
            return None; // 空路径返回None
        }

        let mut results = Vec::new();
        let max_paths = path_num.unwrap_or(6);
        let mut gas = Gas::new();

        // 遍历每个MEV路径寻找有利可图的套利机会
        for (i, mev) in mev_paths.iter().take(max_paths).enumerate() {
            // 根据交易方向确定稳定币token
            let stable_token = if order == Order::Asc {
                mev.s_in
            } else {
                mev.s_out
            };

            // 计算最优金额 - 使用不同的优化算法
            let optimal_amount = match mev.calc {
                0 => {
                    // 使用V2优化算法（适用于V1和V2池子）
                    mev::find_optimal_amount_v2(pools, cache, &mev, &order)
                },
                _ => {
                    // 使用黄金分割法进行优化计算
                    self.calculate_optimal_amount_with_golden_section(&stable_token, mev, &order, cache)
                }
            };

            // 验证最优金额是否有效
            let optimal_amount_value = match optimal_amount {
                Some(amount) if !amount.is_zero() => amount,
                _ => continue, // 没有找到有利可图的金额，跳过此路径
            };

            // 计算交易结果并更新缓存（懒加载策略）
            gas.set_gas_limit(mev.gas);
            let calc_result = Self::get_amounts_out_and_update_cache(optimal_amount_value, mev, &gas, cache, &order);

            // 如果没有利润，直接跳过这个路径
            let calc_result = match calc_result {
                Some(result) => result,
                None => continue, // 无利润，跳过
            };

            // 创建FindPathResult结果

            let find_path_result = FindPathResult {
                mev : mev.clone(),
                rewards : calc_result,
                order : order.clone(),
            };

            results.push(find_path_result);
        }

        // 如果没有找到任何有利可图的路径，返回None
        if results.is_empty() {
            None
        } else {
            Some(results)
        }
    }

    /// 使用黄金分割法计算最优金额（重构优化版本）
    ///
    /// 重构优化特点：
    /// 1. 根据稳定币token从golden_cfg获取搜索范围
    /// 2. 使用mev.rs中的通用零克隆黄金分割法实现
    /// 3. 直接调用重构后的函数，避免生命周期问题
    /// 4. 完全消除重复代码，提高代码复用性
    /// 5. 保持原有的性能优化和功能完整性
    #[inline(always)]
    fn calculate_optimal_amount_with_golden_section(
        &self,
        stable_token: &Address,
        mev_path: &Mev,
        order: &Order,
        cache: &CacheArbitrage
    ) -> Option<U256> {
        // 从golden_cfg获取搜索范围
        let (min_amount, max_amount) = match CONFIG.golden.get(stable_token) {
            Some(&(min, max)) => (min, max),
            None => {
                // 如果没有配置，使用默认范围
                panic!("no golden cfg for token: {:?}", stable_token);
            }
        };

        // 使用重构后的零克隆黄金分割法实现
        self.find_max_golden(min_amount, max_amount, mev_path, cache, order)
    }

    /// 重构后的零克隆黄金分割法实现（调用mev.rs中的通用函数）
    ///
    /// 这个函数作为适配器，将trash.rs特定的缓存逻辑与mev.rs中的通用算法结合
    #[inline(always)]
    fn find_max_golden(
        &self,
        amount_min: U256,
        amount_max: U256,
        mev_path: &Mev,
        cache: &CacheArbitrage,
        order: &Order
    ) -> Option<U256> {
        use crate::vira::consts::{MAX_GOLDEN_STEPS, U_618, U_1000, UINT112_MAX, U_2};

        let mut step = 0u8;

        // 提前检查最小值是否有利润
        let mut amount_out1 = self.get_amount_out_by_pool(amount_min, mev_path, cache, order)?;
        if amount_out1 < amount_min {
            return None;
        }

        // 初始化搜索区间
        let mut low = amount_min;
        let mut high = amount_max;

        // 使用黄金分割比例 1.618034
        let mut x1 = high - ((high - low) * U_618 / U_1000);
        let mut x2 = low + ((high - low) * U_618 / U_1000);

        // 预先计算初始点的输出值
        let mut amount_out2 = self.get_amount_out_by_pool(x2, mev_path, cache, order)?;

        // 使用 max 来避免溢出
        let mut f1 = amount_out1 + UINT112_MAX - x1;
        let mut f2 = amount_out2 + UINT112_MAX - x2;

        // 优化主循环
        while high > low + amount_min && step < MAX_GOLDEN_STEPS {
            if f1 > f2 {
                high = x2;
                x2 = x1;
                f2 = f1;
                x1 = high - ((high - low) * U_618 / U_1000);
                amount_out1 = self.get_amount_out_by_pool(x1, mev_path, cache, order)?;
                f1 = amount_out1 + UINT112_MAX - x1;
            } else if f1 < f2 {
                low = x1;
                x1 = x2;
                f1 = f2;
                x2 = low + ((high - low) * U_618 / U_1000);
                amount_out2 = self.get_amount_out_by_pool(x2, mev_path, cache, order)?;
                f2 = amount_out2 + UINT112_MAX - x2;
            } else {
                break;
            }
            step += 1;
        }

        // 返回结果
        let result = (high + low) / U_2;
        if result.is_zero() {
            None
        } else {
            Some(result)
        }
    }

    /// 重构后的零克隆池子交换计算（调用mev.rs中的通用函数）
    ///
    /// 这个函数作为适配器，将trash.rs特定的缓存逻辑与mev.rs中的通用算法结合
    #[inline(always)]
    fn get_amount_out_by_pool(
        &self,
        initial_input_amount: U256,
        mev_path: &Mev,
        cache: &CacheArbitrage,
        swap_direction: &Order
    ) -> Option<U256> {
        let mut current_amount = initial_input_amount;
        let pool_count = mev_path.pools.len();

        // 根据交换方向确定处理顺序
        let pool_indices: Vec<usize> = match swap_direction {
            Order::Asc => (0..pool_count).collect(),
            Order::Desc => (0..pool_count).rev().collect(),
        };

        let pools = &VIRA.get().unwrap().sm.pools;
        // 遍历每个池子进行交换模拟，使用mev.rs中的通用函数
        for &pool_index in &pool_indices {
            let pool_info = &mev_path.pools[pool_index];

            // 分别处理缓存和主存储的引用，调用mev.rs中的通用函数
            current_amount = if let Some(cached_pool_ref) = cache.data.get(&pool_info.addr) {
                // 缓存命中：使用DashMap的引用
                mev::simulate_swap_with_pool_ref(&*cached_pool_ref, pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else if let Some(main_pool_ref) = pools.data.get(&pool_info.addr) {
                // 主存储命中：使用HashMap的引用
                mev::simulate_swap_with_pool_ref(&main_pool_ref, pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else {
                // 找不到池子，无法继续计算
                return None;
            };
        }

        Some(current_amount)
    }


    /// 使用黄金分割法计算最优金额（保留原有方法以保持兼容性）
    #[allow(dead_code)]
    fn calculate_golden_amount(
        &self,
        min_amount: U256,
        max_amount: U256,
        mev_path: &Mev,
        order: Order,
        cache: &CacheArbitrage
    ) -> U256 {
        // 创建池子引用向量
        let mut pool_refs = Vec::new();
        let pools = &VIRA.get().unwrap().sm.pools;
        for pool_info in &mev_path.pools {
            if let Some(pool) = cache.data.get(&pool_info.addr) {
                pool_refs.push(pool.clone());
            } else if let Some(pool) = pools.data.get(&pool_info.addr) {
                pool_refs.push(pool.clone());
            } else {
                return U256::ZERO; // 找不到池子
            }
        }

        // 创建引用向量
        let pool_refs_slice: Vec<crate::vira::pool::POOL> = pool_refs.into_iter().collect();

        mev::find_max_golden(min_amount, max_amount, mev_path, &pool_refs_slice, order)
    }

    /// 零克隆优化版本：计算交易输出并更新缓存
    ///
    /// 终极优化特点：
    /// 1. 完全消除池子克隆操作，使用自定义计算函数直接处理引用
    /// 2. 实现懒加载缓存策略，只有确认有利润时才执行昂贵操作
    /// 3. 无利润时返回 None，避免无效计算结果的传递
    /// 4. 通过内联计算避免生命周期问题
    /// 5. 保持与 mev.rs 代码模式的兼容性
    #[inline(always)]
    fn get_amounts_out_and_update_cache(
        amount_in: U256,
        mev_path: &Mev,
        gas: &Gas,
        cache: &mut CacheArbitrage,
        order: &Order
    ) -> Option<AmountCalculationResult> {
        // 获取输入token用于利润计算
        let token_in = match order {
            Order::Asc => mev_path.s_in,
            Order::Desc => mev_path.s_out,
        };

        // 零克隆策略：直接使用引用进行计算，避免创建临时向量
        let mut uncached_addrs = Vec::new(); // 记录需要稍后缓存的池子地址

        // 使用自定义零克隆计算函数，直接处理引用
        let amount_outs = Self::calculate_amounts(
            amount_in,
            mev_path,
            cache,
            order,
            &mut uncached_addrs
        )?; // 如果计算失败（找不到池子），直接返回 None

        let amount_out = amount_outs.last().unwrap_or(&U256::ZERO);

        // 早期退出：如果输出金额不大于输入金额，直接返回 None
        if amount_out <= &amount_in {
            return None;
        }

        // 计算利润
        let profit_amount = amount_out.saturating_sub(amount_in);
        if profit_amount.is_zero() {
            return None;
        }
        //let profit_usd = self.vira.sm.tokens.get(&token_in).usd(profit_amount);
        let cfg = &CONFIG.tokens.get(&token_in).expect("error &CONFIG.tokens.get");
        let profit_usd = Token::to_usd(profit_amount, cfg.decimals, cfg.price);
        let cost = gas.convert_cost_to(&mev_path.s_in);

        // 最小利润要求 $0.005
        if profit_usd < cost.usd || profit_usd - cost.usd < 0.005 {
            return None;
        }
        let pools = &VIRA.get().unwrap().sm.pools;
        // 懒加载缓存策略：只有在确认有利润后，才将未缓存的池子添加到缓存
        // 这里需要重新查找并克隆，但只对有利润的路径执行
        for addr in uncached_addrs {
            if let Some(main_pool) = pools.data.get(&addr) {
                cache.data.insert(addr, main_pool.clone());
            }
        }

        // 更新缓存中池子的储备金状态
        Self::update_cache_reserves(cache, mev_path, &amount_outs, order);

        Some(AmountCalculationResult {
            amounts: amount_outs,
            cost,
            profit : AmountAndUsd { amount: profit_amount, usd: profit_usd },
        })
    }

    /// 零克隆计算函数：通过逐个处理池子避免生命周期问题
    ///
    /// 这个函数通过逐个处理池子，在每次循环中独立处理引用，
    /// 避免了跨循环的生命周期问题，实现接近零克隆的性能
    ///
    /// 参数：
    /// - uncached_addrs: 用于记录需要稍后缓存的池子地址
    ///
    /// 返回：
    /// - Option<Vec<U256>>: 计算成功返回金额数组，失败返回 None
    #[inline(always)]
    fn calculate_amounts(
        initial_input_amount: U256,
        mev_path: &Mev,
        cache: &CacheArbitrage,
        swap_direction: &Order,
        uncached_addrs: &mut Vec<Address>
    ) -> Option<Vec<U256>> {
        let mut amounts = Vec::with_capacity(mev_path.pools.len() + 1);
        amounts.push(initial_input_amount); // 第一个元素是输入金额

        let mut current_amount = initial_input_amount;
        let pool_count = mev_path.pools.len();

        // 根据交换方向确定处理顺序
        let pool_indices: Vec<usize> = match swap_direction {
            Order::Asc => (0..pool_count).collect(),
            Order::Desc => (0..pool_count).rev().collect(),
        };

        let pools = &VIRA.get().unwrap().sm.pools;
        // 遍历每个池子进行交换模拟，每次循环独立处理引用
        for &pool_index in &pool_indices {
            let pool_info = &mev_path.pools[pool_index];

            // 在每次循环中独立处理池子引用，避免生命周期问题
            let amount_out = if let Some(cached_pool) = cache.data.get(&pool_info.addr) {
                // 缓存命中：直接使用引用进行计算
                Self::simulate_swap_with_pool(cached_pool, pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else if let Some(main_pool_ref) = pools.data.get(&pool_info.addr) {
                // 主存储命中：使用引用进行计算，记录地址稍后缓存
                uncached_addrs.push(pool_info.addr);
                Self::simulate_swap_with_pool(main_pool_ref.value(), pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else {
                // 找不到池子，返回 None
                return None;
            };

            current_amount = amount_out;
            amounts.push(amount_out);
        }

        Some(amounts)
    }

    /// 使用池子引用执行交换模拟
    ///
    /// 这个辅助函数封装了交换模拟逻辑，避免代码重复
    ///
    /// 返回：
    /// - Option<U256>: 交换成功返回输出金额，失败返回 None
    #[inline(always)]
    fn simulate_swap_with_pool(
        pool_ref: &crate::vira::pool::POOL,
        pool_info: &crate::vira::status::mev::MevPool,
        mev_path: &Mev,
        pool_index: usize,
        current_amount: U256,
        swap_direction: &Order
    ) -> Option<U256> {
        let pool_data = pool_ref.data();

        // 根据交换方向确定费用、输入和输出token
        let (custom_fee, token_in, token_out) = match swap_direction {
            Order::Asc => (
                pool_info.fee_desc,
                pool_data.tokens[pool_info.in_index].addr,
                pool_data.tokens[pool_info.out_index].addr,
            ),
            Order::Desc => (
                pool_info.fee,
                pool_data.tokens[pool_info.out_index].addr,
                pool_data.tokens[pool_info.in_index].addr,
            ),
        };

        // 执行交换模拟
        match pool_ref.simulate_swap(token_in, token_out, current_amount, Some(custom_fee)) {
            Ok(amount_out) => Some(amount_out),
            Err(_) => None, // 交换失败
        }
    }

    /// 更新缓存中池子的储备金状态
    ///
    /// 根据交易结果更新缓存中每个池子的储备金状态
    ///
    /// 参数：
    /// - cache: 套利缓存对象
    /// - mev_path: MEV 路径信息
    /// - amount_outs: 每一步交换的输出金额数组（第一个元素是输入金额）
    /// - order: 交换方向
    #[inline(always)]
    fn update_cache_reserves(
        cache: &mut CacheArbitrage,
        mev_path: &Mev,
        amount_outs: &[U256],
        order: &Order,
    ) {
        // 根据交换方向确定处理顺序
        let pool_count = mev_path.pools.len();
        let pool_indices: Vec<usize> = match order {
            Order::Asc => (0..pool_count).collect(),
            Order::Desc => (0..pool_count).rev().collect(),
        };

        // 遍历每个池子，按交换顺序更新储备金
        for (step_index, &pool_index) in pool_indices.iter().enumerate() {
            let pool_info = &mev_path.pools[pool_index];

            // 获取缓存中的池子进行状态更新
            if let Some(cached_pool) = cache.data.get_mut(&pool_info.addr) {
                // 获取当前步骤的输入和输出金额
                let amount_in = amount_outs[step_index];     // 当前步骤的输入金额
                let amount_out = amount_outs[step_index + 1]; // 当前步骤的输出金额

                // 根据交换方向确定输入输出token的索引
                let (in_index, out_index) = match order {
                    Order::Asc => (pool_info.in_index, pool_info.out_index),
                    Order::Desc => (pool_info.out_index, pool_info.in_index),
                };

                // 更新池子的储备金状态
                // 注意：这里直接修改缓存中的池子，不影响原始数据
                cached_pool.update_reserve_by_index(in_index, out_index, amount_in, amount_out);
            }
        }
    }


    #[inline(always)]
    async fn send_mevs(&self, mevs : Vec<FindPathResult>) -> Result<(), DEXError>{
        //let op_addr = util::rand_elem(&self.operators).expect("op_addr error");
        //统计总gas和总利润usd
        let mut total_gas = U256::ZERO;
        //let mut total_profit = 0.0;
        for find_path_result in &mevs {
            total_gas += find_path_result.mev.gas;
            //total_profit += find_path_result.rewards.profit.usd;
        }
        let gas = Gas::new();
        //gas.set_gas_limit(total_gas + U256::from(200000));
        let mut total_gas_u64 = (total_gas + U256::from(200000)).to::<u64>();
        //使用total_gas_u64和consts.TRASH_MIN_GAS中最大的一个
        total_gas_u64 = total_gas_u64.max(consts::TRASH_MIN_GAS);

        let op_addr = self.operators[0];
        let nonce = USER.nonce.get_and_increment_nonce(&op_addr);

        let input = Self::encode_mevs_input(&mevs);
        let encode = IVira::pumpSmartCall {raws : input}.abi_encode();

        println!("{}", format!("{} sign tx begin, nonce: {}", now_str(), nonce).black());
        let tx = TransactionRequest::default()
                    .with_from(op_addr)
                    .with_to(CONFIG.contract)
                    
                    .with_input(encode)
                    //如果缺失会动态获取
                    .with_chain_id(STATUS.chain_id())
                    .with_nonce(nonce)
                    .with_gas_limit(total_gas_u64)
                    .with_gas_price(gas.get_gas_price().to::<u128>());

        let provider = &VIRA.get().unwrap().connector.provider;
        //let tx_envelope = tx.build(&provider.wallet()).await.expect("sign tx error");


        // 将CPU密集型签名操作移到专用线程池，避免阻塞异步运行时
        let tx_envelope = {
            let tx_clone = tx.clone();
            let provider_clone = provider.clone();
            
            tokio::task::spawn_blocking(move || {
                // 在阻塞线程中执行签名操作
                tokio::runtime::Handle::current().block_on(async move {
                    tx_clone.build(&provider_clone.wallet()).await
                })
            })
            .await
            .map_err(|e| DEXError::EyreError(eyre::eyre!("签名任务执行失败: {}", e)))?
            .map_err(|e| DEXError::EyreError(eyre::eyre!("交易构建失败: {}", e)))?
        };


        println!("{}", format!("{} sign tx end: {}", now_str(), tx_envelope.hash()).black());
        let pending = provider.send_tx_envelope(tx_envelope).await;
        match pending {
            Ok(pending) => {
                let receipt = pending.get_receipt().await;
                match receipt {
                    Ok(receipt) => {
                        Self::display_logs(&receipt, &mevs);
                    },
                    Err(e) => {
                        println!("Get receipt error: {:?}", e);
                    }
                }
            },
            Err(e) => {
                println!("Send tx error: {:?}", e);
            }
        }
        //panic!("test");
        Ok(())
    }

    #[inline(always)]
    fn encode_mevs_input(mevs : &Vec<FindPathResult>) -> Vec<ViraData::PumpReq> {
        let mut pumps = Vec::with_capacity(mevs.len());

        // 把Vec<FindPathResult>转换成Vec<PoolReq>
        for find_path_result in mevs {
            let mev = &find_path_result.mev;
            let rewards = &find_path_result.rewards;
            let order = &find_path_result.order;
            
            // 生成PoolReq数组
            let mut pairs = Vec::with_capacity(mev.pools.len());

            for pool in &mev.pools {
                let (in_idx, out_idx, fee) = match order {
                    Order::Asc => (pool.in_index, pool.out_index, pool.fee),
                    Order::Desc => (pool.out_index, pool.in_index, pool.fee_desc),
                };
                
                pairs.push(ViraData::PoolReq {
                    addr: pool.addr,
                    version: U256::from(pool.ver),
                    fee,
                    fp: pool.fp,
                    inIndex: U256::from(in_idx),
                    outIndex: U256::from(out_idx),
                });
            }

            // 如果Order是Desc，反转pairs的顺序
            if *order == Order::Desc {
                pairs.reverse();
            }

            pumps.push(ViraData::PumpReq {
                convertEth: if mev.convert_eth { 1 } else { 0 }, // bool转uint8
                calc: mev.calc,
                gasLimit: U24::from(mev.gas),
                cost: U88::from(rewards.cost.amount),
                //amountIn: U112::from(rewards.amounts[0]),
                amountIn: U112::ZERO, //在线计算
                pairs,
            });
        }
        
        pumps
    }

    ///返回收益，消耗，期望收益
    fn display_logs(receipt: &TransactionReceipt, mevs : &Vec<FindPathResult>){
        let logs = receipt.decoded_log::<IVira::PumpResults>().expect("decode vira log error");
        let st = &logs._0;
        let rewards = &logs._1;
        //rewards的每一项对应FindPathResult相同索引位置的s_in, 把rewards转换成对应s_in的usd数量
        let mut sum_usd = 0.0;
        for (i, reward) in rewards.iter().enumerate() {
            if reward.is_zero() { continue; }
            if let Some(find_path_result) = mevs.get(i) {
                let s_in = find_path_result.mev.s_in;
                if let Some(token_config) = CONFIG.tokens.get(&s_in) {
                    let usd_value = Token::to_usd(U256::from(*reward), token_config.decimals, token_config.price);
                    sum_usd += usd_value;
                }
            }
        }
        //计算预期收入
        let expect_reward = mevs.iter().fold(0.0, |acc, x| acc + x.rewards.profit.usd);

        //计算消耗
        let gas_cost = receipt.gas_used as u128 * receipt.effective_gas_price;
        let eth = &CONFIG.eth;
        let gas_cost_usd = Token::to_usd(U256::from(gas_cost), eth.decimals, eth.price);


        //如果sum_usd大于0，sum_usd和st需要打印成加粗蓝色，否则使用普通蓝色
        //sum_usd只显示小数点后3位
        let str = format!("[trash] {} {} ({}) ",
            receipt.transaction_hash,
            receipt.block_number.unwrap_or_default(),
            receipt.transaction_index.unwrap_or_default(),
        ).blue();

        let reward_str = if sum_usd > 0.0 {
            format!("$ {:.3} - {:.3} ({:.3}){:?}", sum_usd, gas_cost_usd, expect_reward, st).bright_blue()
        } else {
            format!(" - {:.3} ({:.3}) {:?}", gas_cost_usd, expect_reward, st).blue()
        };
        println!("{}{}", str, reward_str);
        let (total_profit, total_cost, total_expect, tick)  = USER.add_trash_records(sum_usd, gas_cost_usd, expect_reward);
        if tick % 1 == 0 {
            //根据USER.boot_time计算已经运行的时间，格式是HH:MM:SS

            println!("{}", format!("[trash] total: $ {:.3} - {:.3} ($ {:.1}, tick: {})", total_profit, total_cost, total_expect, tick).underline().purple());
        }

    }
}

// 包含性能测试模块
#[cfg(test)]
mod tests {
}


