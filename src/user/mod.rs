use std::{collections::HashMap, sync::{atomic::{AtomicU32, AtomicU64, Ordering}, RwLock}};

use alloy::primitives::{Address, U256};
use serde::{Deserialize, Serialize};

use crate::connector::nonce::NonceManager;

#[derive(Debug, Default)]
pub struct Profit {
    pub profit : AtomicU64,
    pub cost : AtomicU64,
    pub expect : AtomicU64,
    pub tick : AtomicU32, //套利次数
}

#[derive(Debug, <PERSON><PERSON>ult)]
pub struct Profits {
    trash : Profit,
    pump : Profit,
    junk : Profit,
}

#[derive(Debug, Default)]
pub struct User {
    pub balances : RwLock<HashMap<Address, U256>>,
    pub nonce : NonceManager,

    pub profits : Profits,

    pub boot_time : u64,
}

impl User {
    pub fn new() -> Self {
        User {
            nonce : NonceManager::new(),
            boot_time : std::time::SystemTime::now().duration_since(std::time::UNIX_EPOCH).unwrap().as_secs(),
            ..Default::default()
        }
    }
}

impl User {
    pub fn split_amounts(balances : HashMap<Address, U256>, split: Option<usize>) -> (Vec<Address>, Vec<U256>) {
        let split = split.unwrap_or(1);
        // 把balances的keys和values分别生成两个新的vec!
        let stable_addrs: Vec<_> = balances.keys().cloned().collect();
        let stable_amounts: Vec<_> = balances.values().cloned().map(|v| v / U256::from(split)).collect();

        if stable_amounts.iter().any(|v| *v == U256::ZERO) {
            panic!("balances has zero value");
        }

        if stable_addrs.len() == 0 {
            panic!("stable_addrs is empty");
        }

        (stable_addrs, stable_amounts)
    }

    pub fn add_trash_records(&self, profit: f32, cost: f32, expect: f32) -> (f32, f32, f32, u32) {
        let profit_scaled = (profit * 10000.0) as u64;
        let cost_scaled = (cost * 10000.0) as u64;
        let expect_scaled = (expect * 10000.0) as u64;
        
        let new_profit = self.profits.trash.profit.fetch_add(profit_scaled, Ordering::SeqCst) + profit_scaled;
        let new_cost = self.profits.trash.cost.fetch_add(cost_scaled, Ordering::SeqCst) + cost_scaled;
        let new_expect = self.profits.trash.expect.fetch_add(expect_scaled, Ordering::SeqCst) + expect_scaled;
        let new_tick = self.profits.trash.tick.fetch_add(1, Ordering::SeqCst) + 1;
        
        (
            (new_profit as f64 / 10000.0) as f32,
            (new_cost as f64 / 10000.0) as f32,
            (new_expect as f64 / 10000.0) as f32,
            new_tick
        )
    }
}