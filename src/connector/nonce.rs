use std::collections::HashMap;
use std::str::FromStr;
use std::sync::atomic::AtomicU64;
use std::sync::atomic::Ordering;
use std::sync::atomic::AtomicUsize;
use alloy::primitives::Address;
use alloy::providers::Provider;
use colored::Colorize;

use crate::CONFIG;
use crate::VIRA;

#[derive(Debug, Default)]
pub struct NonceManager {
    pub nonces : HashMap<Address, AtomicU64>,
}

impl NonceManager {
    /// 需要在CONFIG初始化后才能执行，把CONFIG的所有钱包加入到数据中
    pub fn new() -> Self {
        let mut count = 0;
        let mut nonces = HashMap::new();
        for operator_group in &CONFIG.operators {
            for (address_str, _) in operator_group {
                let address = Address::from_str(address_str).expect("Invalid operator address");
                nonces.insert(address, AtomicU64::new(0));
                count += 1;
            }
        }
        println!(" ({}) account init nonce.", count);
        Self {
            nonces : nonces,
        }
    }

    pub async fn update_all(&self) {
        for address in self.nonces.keys() {
            self.update(address).await;
        }
    }

    pub async fn update(&self, address: &Address) {
        if let Some(n) = self.nonces.get(address) {
            let provider = &VIRA.get().unwrap().connector.provider;
            let nonce = provider.get_transaction_count(*address).await.expect("get nonce error");
            n.store(nonce, Ordering::Relaxed);
            println!("{}", format!("{} n:{}", address, nonce).black())
        }
    }

    pub fn get_and_increment_nonce(&self, address: &Address) -> u64 {
        if let Some(nonce) = self.nonces.get(address) {
            nonce.fetch_add(1, Ordering::Relaxed)
        } else {
            println!("error loading nonce for address: {:?}", address);
            0
        }
    }

    pub fn get_nonce(&self, address: &Address) -> u64 {
         self.nonces
            .get(address)
            .map(|nonce| nonce.load(Ordering::Relaxed))
            .unwrap_or(0)
    }

    pub fn increment_nonce(&self, address: &Address) -> u64 {
        if let Some(nonce) = self.nonces.get(address) {
            nonce.fetch_add(1, Ordering::Relaxed)
        } else {
            0
        }
    }

    pub fn set_nonce(&self, address: &Address, nonce: u64) {
        if let Some(n) = self.nonces.get(address) {
            n.store(nonce, Ordering::Relaxed);
        }
    }
}