use std::borrow::Borrow;
use alloy::primitives::U256;
use alloy::primitives::utils::format_units;
use colored::Colorize;

use crate::strategy::trash::FindPathResult;
use crate::vira::status::mev::{Mev, MevStatus, Order};
use crate::vira::token::TokenManager;
use super::PoolIndex;

/// 显示工具
/// 提供格式化和展示功能

/// 打印 MEV 路径
/// 
/// 该函数用于格式化并打印MEV路径信息，包括状态、路径和权重等信息
/// 
/// # 参数
/// * `mevs` - MEV路径的引用向量
/// 
/// # 优化说明
/// 1. 减少不必要的内存分配和克隆操作
/// 2. 通过引用传递优化参数
/// 3. 添加详细的中文注释，使用描述性变量名
/// 4. 改进输出格式的清晰度和可读性
pub fn display_mevs(mevs: &[Mev]) {
    // 遍历所有MEV路径
    for mev in mevs {
        // 根据MEV状态确定显示颜色
        // status_desc 状态描述
        let status_desc_color = if mev.status_desc == MevStatus::Active {
            "Active".green()
        } else {
            "Bad".red()
        };

        // status 状态
        let status_color = if mev.status == MevStatus::Active {
            "Active".green()
        } else {
            "Bad".red()
        };

        // 组合状态字符串
        let status_string = format!("[{},{}]", status_desc_color, status_color);

        // 构建路径字符串，避免不必要的内存分配
        let path_string: String = mev
            .details
            .iter()
            .map(|(symbol, _)| symbol.as_str()) // 只取符号，忽略小数位数
            .collect::<Vec<&str>>()
            .join(" -> ");

        // 打印格式化的MEV信息
        // 突出显示重要信息：状态、路径和权重
        println!("{} {},  gas :{}  weight: $ {:.3}", status_string, path_string, mev.gas.to_string(), mev.weight);
        println!("{}", format!("{}", mev.pools.iter().map(|p| p.addr.to_string()).collect::<Vec<String>>().join(", ")).black());
    }
}

/// 显示查找路径结果
/// 
/// 该函数用于格式化并打印套利路径查找结果，包括路径、利润和gas消耗等信息
/// 
/// # 参数
/// * `find_results` - 查找路径结果的引用向量
/// 
/// # 优化说明
/// 1. 减少不必要的内存分配
/// 2. 通过引用传递优化参数
/// 3. 添加边界检查和错误处理
/// 4. 保持与项目中其他MEV相关代码的一致性
pub fn display_find_path_result(find_results: &[FindPathResult]) {
    // 遍历所有查找结果
    for find_result in find_results {
        let mev = &find_result.mev;
        let rewards = &find_result.rewards;
        
        // 边界检查：确保amounts不为空
        if rewards.amounts.is_empty() {
            eprintln!("警告: MEV结果中的金额数组为空");
            continue;
        }
        
        // 根据交易方向确定代币显示顺序
        let tokens: Vec<&(String, u8)> = if find_result.order == Order::Asc {
            // 正向交易：按原顺序处理
            mev.details.iter().collect()
        } else {
            // 反向交易：反转顺序处理
            mev.details.iter().rev().collect()
        };

        // 格式化路径中的每个代币及其对应的金额
        let formatted_path_tokens: Vec<String> = tokens
            .iter()
            .zip(rewards.amounts.iter())
            .map(|(token_info, amount)| {
                format!("{}({})", token_info.0, format_units_to_3_decimal(*amount, token_info.1))
            })
            .collect();

        // 构建路径字符串
        let path_string = formatted_path_tokens.join(" -> ");
        
        // 获取第一个代币的小数位数用于格式化利润
        // 添加边界检查
        let first_token_decimal = if !tokens.is_empty() {
            tokens[0].1
        } else {
            18 // 默认ERC20代币小数位数
        };
        
        // 格式化利润信息（使用绿色突出显示正利润）
        let profit_string = format!("+{}", format_units_to_3_decimal(rewards.profit.amount, first_token_decimal)).green();
    
        // 格式化美元价值（使用蓝色突出显示）
        let usd_value_string = format!("${:.3}", rewards.profit.usd).blue();
        
        // 格式化Gas消耗
        let gas_string = format!("g:{}", mev.gas).black();

        let cost_string = format!("cost:${:.3}", rewards.cost.usd).black();
        
        // 输出完整信息，使用不同的颜色突出重要信息
        println!("{}  {} {} {} {}", path_string, profit_string, gas_string, cost_string, usd_value_string);
    }
}

/// 格式化U256数值为指定小数位数的字符串，保留3位小数
/// 
/// # 参数
/// * `value` - 需要格式化的U256数值
/// * `decimals` - 代币的小数位数
/// 
/// # 返回值
/// * `String` - 格式化后的字符串
/// 
/// # 优化说明
/// 1. 添加输入验证
/// 2. 改进错误处理
/// 3. 添加详细注释
fn format_units_to_3_decimal(value: U256, decimals: u8) -> String {
    // 调用alloy库的format_units函数进行基本格式化
    match format_units(value, decimals) {
        Ok(formatted_string) => {
            // 将格式化后的字符串解析为f64数值
            match formatted_string.parse::<f64>() {
                Ok(numeric_value) => {
                    // 格式化为保留3位小数的字符串
                    format!("{:.3}", numeric_value)
                },
                Err(parse_error) => {
                    // 解析失败时记录错误并返回默认值
                    eprintln!("数值解析错误: {}", parse_error);
                    "0.000".to_string()
                }
            }
        },
        Err(format_error) => {
            // 格式化失败时记录错误并返回默认值
            eprintln!("单位格式化错误: {}", format_error);
            "0.000".to_string()
        }
    }
}

/// 打印池子索引链
/// 
/// 该函数用于格式化并打印池子索引链信息
/// 
/// # 参数
/// * `pool_indexs` - 池子索引的引用切片
/// * `tokens` - 代币管理器的引用
/// * `str` - 可选的附加字符串信息
/// 
/// # 泛型约束
/// * `T` - 必须实现Borrow<PoolIndex>和Debug trait
pub fn display_pool_indexs<T>(pool_indexs: &[T], tokens: &TokenManager, str: Option<String>)
where
    T: Borrow<PoolIndex> + std::fmt::Debug,
{
    // 遍历所有池子索引
    for pool_index in pool_indexs {
        // 获取输入和输出代币信息
        let in_token = tokens.data.get(&pool_index.borrow().in_token).expect("error get in token");
        let out_token = tokens.data.get(&pool_index.borrow().out_token).expect("error get out token");
        
        // 打印代币符号连接
        print!("{}->{} ", in_token.symbol, out_token.symbol);
    }
    
    // 打印附加字符串信息（如果存在）
    if let Some(additional_info) = str {
        print!("{}", additional_info);
    }
    
    // 换行结束输出
    println!();
}
