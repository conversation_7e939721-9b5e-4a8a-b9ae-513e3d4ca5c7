//! 同步状态
//! 
//! 该模块负责同步状态，包括检查池子的fee和同步池子
//! 
//! 主要函数:
//! - sync: 同步所有pool的数据
//! - check_pools_fee: 检查池子的fee
//! - process_fee_results: 处理fee结果
//! - process_non_stable_pools: 处理非稳定币池子

use std::sync::Arc;

use alloy::primitives::{Address, U256};
use alloy::{eips::BlockId, providers::Provider};
use futures::stream::StreamExt;
use futures::stream::iter;
use colored::Colorize;
use std::collections::HashSet;
use std::sync::atomic::{AtomicUsize, Ordering};

use crate::vira::dex;
use crate::vira::status::mev::MevStatus;
use crate::vira::status::pools::Pools;
use crate::vira::status::value_calc;
use crate::CONFIG;
use crate::{connector::Connector, vira::{consts::{BATCH_CHECK_SIZE, U_2000, U_666666, U_900004, U_900005, U_ZERO, BATCH_CHECK_MEV_SIZE}, contract::{Contract, ViraData, ViraLogic}, dex::{factory::{DexFactory, FACTORY}}, errors::DEXError, pool::{DexPool, Status, POOL}, status::mev::{MevPool}}};

// 类型别名，简化使用
type PoolReq = ViraData::PoolReq;

use super::StatusManager;


/// 更新所有pool的reserves
pub async fn sync_all_reserves(pools : &Pools, connector: &Connector) -> Result<(), DEXError> {
    println!("sync_all_reserves");
    // 并发限制常量
    const MAX_CONCURRENT_TASKS: usize = 20;
    
    let mut v2_addrs = Vec::new();
    let mut v3_addrs = Vec::new();
    
    // 只收集地址，不克隆整个池子对象
    for item in pools.data.iter() {
        let pool = item.value();
        match pool {
            POOL::UniV2Pool(_) => v2_addrs.push(pool.addr()),
            POOL::UniV3Pool(_) => v3_addrs.push(pool.addr()),
        }
    }
    
    println!("同步池子reserves: V2池子数量: {}, V3池子数量: {}", v2_addrs.len(), v3_addrs.len());
    
    // 处理V2池子地址
    if !v2_addrs.is_empty() {
        let total_v2_pools = v2_addrs.len();
        let processed_counter = Arc::new(AtomicUsize::new(0));
        
        // 将v2_addrs分割成多个批次
        let v2_chunks: Vec<Vec<Address>> = v2_addrs
            .chunks(BATCH_CHECK_SIZE)
            .map(|chunk| chunk.to_vec())
            .collect();
        
        println!("开始同步V2池子reserves, 共{}个批次", v2_chunks.len());
        
        // 使用buffer_unordered并发处理多个批次
        let futures = iter(v2_chunks.into_iter().map(|addrs| {
            let processed_counter_clone = processed_counter.clone();
            
            async move {
                let chunk_size = addrs.len();
                match dex::uni_v2::contract::get_pool_reserves_batch_request(addrs.clone(), connector).await {
                    Ok(reserves) => {
                        let current = processed_counter_clone.fetch_add(chunk_size, Ordering::SeqCst);
                        eprint!("\r同步V2池子reserves进度: {}/{} ({:.2}%)", 
                            current + chunk_size, 
                            total_v2_pools,
                            ((current + chunk_size) as f64 / total_v2_pools as f64) * 100.0
                        );
                        
                        Ok((addrs, reserves))
                    },
                    Err(e) => {
                        eprintln!("\nV2池子批次同步错误: {:?}", e);
                        Err(e)
                    }
                }
            }
        }))
        .buffer_unordered(MAX_CONCURRENT_TASKS);
        
        futures::pin_mut!(futures);
        
        // 处理结果
        while let Some(result) = futures.next().await {
            match result {
                Ok((addrs, reserves)) => {
                    // 更新池子数据
                    for (i, addr) in addrs.iter().enumerate() {
                        if let Some(mut pool) = pools.data.get_mut(addr) {
                            let (reserve0, reserve1, timestamp) = reserves[i];
                            if let POOL::UniV2Pool(ref mut v2pool) = *pool.value_mut() {
                                v2pool.data.tokens[0].reserve = reserve0;
                                v2pool.data.tokens[1].reserve = reserve1;
                                v2pool.data.update_time = timestamp as u64;
                            }
                        }
                    }
                },
                Err(e) => {
                    eprintln!("\nV2池子同步错误: {:?}", e);
                }
            }
        }
        
        println!("\nV2池子reserves同步完成");
    }
    
    // 处理V3池子地址 (当V3 sync_reserves实现后再完善)
    if !v3_addrs.is_empty() {
        println!("注意: V3池子同步尚未完全实现，共有 {} 个V3池子", v3_addrs.len());
        // 这里未来实现V3池子的reserves同步
    }

    Ok(())
}


//核心逻辑: 同步最新的pool addr并且更新所需data
pub async fn sync_new_pools(contract : &Contract, sm :&mut StatusManager, connector : &Connector) -> Result<Vec<Address>, DEXError> {
    let chain_tip = BlockId::from(connector.provider.get_block_number().await?);
    let factories = sm.factories.clone();
    
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(factories.into_iter().map(|(_, mut factory)| {
        async move {
            let mut discovered_amms = factory.discover(chain_tip, connector).await?;

            discovered_amms = factory.sync(discovered_amms, chain_tip, connector).await?;

            Ok::<(Vec<POOL>, FACTORY), DEXError>((discovered_amms, factory))
        }
    }))
    .buffer_unordered(CONFIG.concurrency.max_task);

    let mut new_pools = Vec::new();
    let mut new_addrs = Vec::new();
    futures::pin_mut!(futures);
    while let Some(res) = futures.next().await {
        let (synced_amms, factory) = res?;
        //把synced_amms的所有地址添加到new_addrs
        new_addrs.extend(synced_amms.iter().map(|p| p.addr()));
        new_pools.extend(synced_amms);
        sm.factories.insert(factory.address(), factory); //更新factory的索引到当前更新到的位置
    }
    
    check_all_pools_fee(contract, new_pools, sm).await?;

    Ok(new_addrs)
}


/// 检查池子的fee
/// 
/// 该函数接收一个池子列表、状态管理器和连接器，
/// 并对每个池子进行费用检查。检查的结果将更新到pools并且添加到sm中
/// 
/// 参数:
/// - `pools`: 需要检查费用的池子列表
/// - `sm`: 状态管理器，用于管理池子的状态
/// - `connector`: 连接器，用于与区块链进行交互
/// 
/// 返回:
/// - `Result<(), DEXError>`: 成功时返回 Ok，失败时返回 DEXError
/// 
/// 核心逻辑:
/// 1. 遍历pools，把包含stable的pools分成一组，每BATCH_SIZE数量的pools使用contract.batch_check_pair_fee检查fee, 根据返回的结果设置pool的状态
///    - 默认状态为Status::Good
///         1) fee == 900004 (通缩币) Status::OddBad;
///         2) fee == 900005 (未知状态) Status::Unknown;
///         3) fee > 2000 Status::Bad;
///         4) 0 < fee < 2000 Status::Fee;
///         5) fee == 666666 (蜜罐币) Status::OddGood;
///         6) 其余情况保持Good状态
/// 2. 把OddGood和Good的pool增加到sm中
/// 3. 检查剩余的uncheck pool, 逻辑参考 checkpoint::process_non_stable_pools
/// 4. 把剩余pools中状态为OddGood和Good的pool增加到sm中
/// 5. 重复一次步骤3和4
/// 6. 动态打印结果

pub async fn check_all_pools_fee(contract: &Contract, pools: Vec<POOL>, sm: &mut StatusManager) -> Result<(), DEXError> {
    println!("{}", "\nChecking pools fee...".green());

    // 统计总池子数量
    let total_pools = pools.len();
    println!("Total pools to check: {}", total_pools);

    // 将池子分成两组：包含稳定币和不包含稳定币的
    let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
    
    let (stable_pools, non_stable_pools): (Vec<_>, Vec<_>) = pools
        .into_iter()
        .partition(|pool| {
            pool.data().tokens.iter().any(|token| {
                stables.contains(&token.addr)
            })
        });
    
    let stable_len = stable_pools.len();
    let non_stable_len = non_stable_pools.len();
    
    println!("Stable pools: {}, Non-stable pools: {}", 
        stable_len, 
        non_stable_len
    );

    // 处理所有池子的进度计数器
    let processed = Arc::new(AtomicUsize::new(0));

    // 1. 处理包含稳定币的池子
    let stable_pools_count = check_stable_pools(stable_pools, sm, contract, processed.clone()).await?;
    
    // 2. 处理不包含稳定币的池子
    let non_stable_pools_count = check_non_stable_pools(
        non_stable_pools, 
        contract,
        sm, 
        processed.clone(),
        stable_len, // 开始索引从stable_pools.len()开始
    ).await?;

    println!("\nPool fee checking completed! Total processed: {}", 
        stable_pools_count + non_stable_pools_count);
    
    Ok(())
}

/// 处理包含稳定币的池子，并发处理多个批次
async fn check_stable_pools(
    stable_pools: Vec<POOL>,
    sm: &mut StatusManager,
    contract: &Contract,
    processed_counter: Arc<AtomicUsize>
) -> Result<usize, DEXError> {
    if stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nChecking stable pools...");

    // 根据 USD 价值过滤稳定币池
    let filtered_pools = filter_low_value_pools(stable_pools);
    let total = filtered_pools.len();
    let chunks: Vec<Vec<POOL>> = filtered_pools
        .chunks(BATCH_CHECK_SIZE)
        .map(|chunk| chunk.to_vec())
        .collect();
    
    // 重置处理计数器确保百分比计算正确
    processed_counter.store(0, Ordering::SeqCst);
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(chunks.into_iter().enumerate().map(|(_, chunk)| {
        let contract_clone = contract;
        let processed_counter_clone = processed_counter.clone();
        
        async move {
            let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
            let mut pool_reqs: Vec<ViraLogic::CheckPairFeeInputDesc> = Vec::with_capacity(chunk.len());
            for pool in &chunk {
                let data = pool.data();
                let stable_index = data
                    .tokens
                    .iter()
                    .position(|t| stables.contains(&t.addr))
                    .unwrap_or(0);

                pool_reqs.push(ViraLogic::CheckPairFeeInputDesc {
                    prePair: vec![],
                    pair: vec![PoolReq {
                        addr: data.addr,
                        version: U256::from(data.ver),
                        fee: U_ZERO,
                        fp: data.fp,
                        inIndex: U256::from(stable_index),
                        outIndex: U_ZERO, //在合约中会遍历outIndex，请求数据只需要设置成0即可
                    }],
                });
            }
            if pool_reqs.is_empty() {
                return Ok::<(Vec<POOL>, Vec<Vec<alloy::primitives::U256>>), DEXError>((chunk, vec![]));
            }
            match contract_clone.batch_check_pair_fee(pool_reqs).await {
                Ok(fees) => {
                    let current = processed_counter_clone.fetch_add(chunk.len(), Ordering::SeqCst);
                    eprint!("\r处理稳定币池子进度: {}/{} ({:.2}%)", 
                        current + chunk.len(), 
                        total,
                        ((current + chunk.len()) as f64 / total as f64) * 100.0
                    );
                    
                    Ok((chunk, fees))
                },
                Err(e) => Err(e)
            }
        }
    }))
    .buffer_unordered(CONFIG.concurrency.max_task);
    
    let mut processed_pools = Vec::new();
    let mut processed_count = 0;
    
    // 收集并处理结果
    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok((chunk, fees)) => {
                if !fees.is_empty() {
                    let mut chunk_copy = chunk.clone();
                    apply_fee_results(&mut chunk_copy, &fees, sm, processed_count);
                    processed_count += chunk.len();
                    processed_pools.extend(chunk);
                }
            },
            Err(e) => {
                println!("\n{}", format!("Error processing stable pools batch: {:?}", e).red());
                return Err(e);
            }
        }
    }
    
    println!("\n稳定币池子处理完成: 总共处理 {}/{} ({:.2}%)",
        processed_count, 
        total,
        (processed_count as f64 / total as f64) * 100.0
    );
    
    Ok(processed_count)
}

/// 根据 USD 价值过滤稳定币池
///
/// 该函数计算每个稳定币池的 USD 价值，并过滤掉价值小于 $0.001 的池
///
/// 参数:
/// - `stable_pools`: 包含稳定币的池子列表
///
/// 返回:
/// - `Vec<POOL>`: 过滤后的池子列表
fn filter_low_value_pools(stable_pools: Vec<POOL>) -> Vec<POOL> {
    let mut filtered_pools = Vec::new();
    let mut filtered_count = 0;

    //println!("开始根据 USD 价值过滤稳定币池...");

    for pool in stable_pools {
        let data = pool.data();
        let mut is_low_value = false;
        let mut found_stable = false;

        // 遍历池中的每个 token，寻找稳定币
        for token in &data.tokens {
            if CONFIG.golden.contains_key(&token.addr) {
                let golden = CONFIG.golden.get(&token.addr).unwrap();
                is_low_value = token.reserve < golden.0;
                found_stable = true;
                break;
            }
        }

        // 如果找到稳定币且价值满足条件，则保留该池
        if found_stable && !is_low_value {
            filtered_pools.push(pool);
        } else {
            filtered_count += 1;
            // 可选：打印被过滤的池信息（用于调试）
            if found_stable {
                //println!("过滤低价值池: {})", data.addr);
            } else {
                println!("过滤无稳定币信息池: {}", data.addr);
            }
        }
    }

    println!("USD 价值过滤完成: 保留 {} 个池，过滤掉 {} 个低价值池",
        filtered_pools.len(), filtered_count);

    filtered_pools
}


/// 并发处理非稳定币池子
async fn check_non_stable_pools(
    non_stable_pools: Vec<POOL>,
    contract: &Contract,
    sm: &mut StatusManager,
    processed_counter: Arc<AtomicUsize>,
    start_index: usize,
) -> Result<usize, DEXError> {
    if non_stable_pools.is_empty() {
        return Ok(0);
    }

    if non_stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing non-stable pools (1st round)...");
    
    // 第一轮处理
    let (processed_first, remaining_pools) = check_non_stable_pools_round(
        non_stable_pools,
        contract,
        sm,
        processed_counter.clone(),
        start_index,
        false
    ).await?;
        
    if remaining_pools.is_empty() {
        return Ok(processed_first);
    }
    
    // 第二轮处理
    println!("\nProcessing non-stable pools (2nd round)...");
    let (processed_second, _) = check_non_stable_pools_round(
        remaining_pools,
        contract,
        sm,
        processed_counter,
        start_index + processed_first,
        true
    ).await?;
    
    let final_count = processed_first + processed_second;
    // (暂时去除)标记剩余未检查的池子为NoStable, 有用的pool都已经添加到sm当中
    /*
    for mut pool in remaining_second {
        if pool.data().status == Status::UnChecked {
            pool.data_mut().status = Status::NoStable;
            final_count += 1;
        }
    }
    */
    Ok(final_count)
}

/// 并发处理非稳定币池子的单轮
async fn check_non_stable_pools_round(
    pools: Vec<POOL>,
    contract: &Contract,
    sm: &mut StatusManager,
    processed_counter: Arc<AtomicUsize>,
    start_index: usize,
    is_second_round: bool
) -> Result<(usize, Vec<POOL>), DEXError> {
    let total = pools.len();
    if total == 0 {
        return Ok((0, vec![]));
    }

    let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
    
    let round_name = if is_second_round { "2nd round" } else { "1st round" };
    println!("Processing {} non-stable pools ({})", total, round_name);
    
    // 先预处理所有的请求，将pools分成两部分：有pre_pairs的和无pre_pairs的
    let mut all_requests = Vec::with_capacity(pools.len());
    let mut unchecked_pools = Vec::new();
    
    for pool in pools {
        let data = pool.data();
        let mut found_path = false;
        
        // 遍历pool中的每个token，寻找到stable的路径
        for (token_idx, token) in data.tokens.iter().enumerate() {
            let path = sm.index.get_path(&token.addr, &stables, &sm.pools, None, None, false);

            // 如果找到路径，将所有池子作为pre_pair, 并且中断循环
            if !path.is_empty() {
                let stable = path.last().unwrap().out_token;
                //根据稳定币路径获取path和pool的每一跳的reserves
                let mut reserves: Vec<(U256, U256)> = path.iter()
                    .rev()  // 反转整个路径顺序
                    .map(|pool_index| {
                        let p = sm.pools.data.get(&pool_index.addr).unwrap();
                        p.reserve_by_index(pool_index.out_index, pool_index.in_index)
                    })
                    .collect();
                reserves.push((token.reserve, U256::ZERO));
                let vals = value_calc::ValueCalc::calc_values(reserves);
                let min_val = CONFIG.golden.get(&stable).unwrap().0;
                if vals.last().unwrap() < &min_val {
                    //println!("过滤低价值池: {}, is_second_round: {}", data.addr, is_second_round);
                    break;
                }


                let pre_pairs: Vec<PoolReq> = path.iter()
                    .rev()  // 反转整个路径顺序
                    .map(|pool_index|
                        PoolReq {
                            addr: pool_index.addr,
                            version: U256::from(sm.pools.data.get(&pool_index.addr).unwrap().data().ver),
                            fee: U256::ZERO,
                            fp: sm.pools.data.get(&pool_index.addr).unwrap().data().fp,
                            inIndex: U256::from(pool_index.out_index),  // 反转 index
                            outIndex: U256::from(pool_index.in_index),  // 反转 index
                        })
                        .collect();

                // 创建当前池子的请求
                let pool_req = ViraLogic::CheckPairFeeInputDesc {
                    prePair: pre_pairs,
                    pair: vec![PoolReq {
                        addr: data.addr,
                        version: U256::from(data.ver),
                        fee: U_ZERO,
                        fp: data.fp,
                        inIndex: U256::from(token_idx),
                        outIndex: U_ZERO, //在合约中会遍历outIndex，请求数据只需要设置成0即可
                    }],
                };
                
                all_requests.push((pool.clone(), pool_req));
                found_path = true;
                break;
            }
        }
        
        // 如果没有找到路径，直接将池子加入到unchecked_pools
        if !found_path {
            unchecked_pools.push(pool);
        }
    }
    
    println!("Found {} pools with path to stables, {} pools without path", 
        all_requests.len(), unchecked_pools.len());
    
    // 如果没有可处理的请求，直接返回
    if all_requests.is_empty() {
        return Ok((0, unchecked_pools));
    }
    
    // 重置处理计数器，确保百分比计算正确
    processed_counter.store(0, Ordering::SeqCst);
    let requests_total = all_requests.len();
    
    // 将请求分成更小的批次进行并发处理
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(all_requests.chunks(BATCH_CHECK_SIZE).enumerate().map(|(batch_idx, batch)| {
        let mut valid_reqs = Vec::new();
        let mut pool_mapping = Vec::new();
        
        // 只处理有效的请求
        for (pool, req) in batch {
            valid_reqs.push(req.clone());
            pool_mapping.push(pool.clone());
        }

        let contract_clone = contract;
        let processed_counter_clone = processed_counter.clone();
        let batch_start_idx = start_index + batch_idx * BATCH_CHECK_SIZE;
        
        async move {
            match contract_clone.batch_check_pair_fee(valid_reqs).await {
                Ok(fees) => {
                    let current = processed_counter_clone.fetch_add(pool_mapping.len(), Ordering::SeqCst);
                    eprint!("\r处理非稳定币池子进度 ({}): {}/{} ({:.2}%)", 
                        round_name,
                        current + pool_mapping.len(), 
                        requests_total,
                        ((current + pool_mapping.len()) as f64 / requests_total as f64) * 100.0
                    );
                    
                    Ok((pool_mapping, fees, batch_start_idx))
                },
                Err(e) => {
                    panic!("{}", format!("Error checking fees for pools batch: {:?}", e).red());
                }
            }
        }
    }))
    .buffer_unordered(CONFIG.concurrency.max_task);
    
    let mut processed_count = 0;
    let mut processed_pools = Vec::new();
    
    // 收集并处理结果
    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok((pool_mapping, fees, batch_start_idx)) => {
                if !fees.is_empty() {
                    let mut pools_to_process = pool_mapping.clone();
                    apply_fee_results(&mut pools_to_process, &fees, sm, batch_start_idx);
                    processed_count += fees.len();
                    processed_pools.extend(pool_mapping);
                }
            },
            Err(e) => {
                println!("\n{}", format!("Error processing non-stable pools batch: {:?}", e).red());
                return Err(e);
            }
        }
    }
    
    println!("\n非稳定币池子处理完成: 总共处理 {}/{} ({:.2}%)",
        processed_count, 
        requests_total,
        (processed_count as f64 / requests_total as f64) * 100.0
    );
    
    // 直接返回预先分离的未检查池子
    Ok((processed_count, unchecked_pools))
}

// 处理fee结果的辅助函数
fn apply_fee_results(
    chunk: &mut [POOL], 
    fees: &[Vec<alloy::primitives::U256>], 
    sm: &mut StatusManager,
    start_index: usize
) {
    // 批量收集需要添加到 StatusManager 的池子
    let mut pools_to_add = Vec::new();

    for (i, (pool, fee)) in chunk.iter_mut().zip(fees).enumerate() {
        let data = pool.data_mut();
        
        // 先设置所有token的fee值
        let mut has_honey = false;
        for (token, &val) in data.tokens.iter_mut().zip(fee.iter()) {
            // 检查是否是蜜罐币
            if val == U_666666 {
                has_honey = true;
                token.fee = U_ZERO; // 蜜罐币的fee设为0
            } else {
                token.fee = val;
            }
        }

        // 默认状态为Good
        let mut current_status = Status::Good;
        
        // 按照优先级判断状态
        // 1. 检查是否存在900004 (通缩币)
        if data.tokens.iter().any(|t| t.fee == U_900004) {
            current_status = Status::OddBad;
        }
        // 2. 检查是否存在900005 (未知状态)
        else if data.tokens.iter().any(|t| t.fee == U_900005) {
            current_status = Status::Unknown;
        }
        // 3. 检查是否有fee大于2000
        else if data.tokens.iter().any(|t| t.fee > U_2000) {
            current_status = Status::Bad;
        }
        // 4. 检查是否有fee大于0小于2000
        else if data.tokens.iter().any(|t| t.fee > U_ZERO && t.fee <= U_2000) {
            current_status = Status::Fee;
        }
        // 5. 检查是否存在666666 (Honey pot)
        else if has_honey {
            current_status = Status::OddGood;
        }
        // 6. 其余情况保持Good状态
        
        // 设置状态
        data.status = current_status;

        // 打印结果
        let fee_info = data.tokens.iter()
            .map(|token| {
                if token.fee == U_ZERO {
                    token.symbol.to_string()
                } else {
                    format!("{} ({})", token.symbol, token.fee)
                }
            })
            .collect::<Vec<_>>()
            .join(" - ");

        let line = format!("({}) {} {}", start_index + i, data.addr, fee_info);
        let colored_line = match data.status {
            Status::Fee => line.green().to_string(),
            Status::Bad => line.red().to_string(),
            Status::OddGood => line.yellow().to_string(),
            Status::OddBad => line.red().bold().to_string(),
            Status::Unknown => line.bright_purple().to_string(),
            Status::NoStable => line.black().to_string(),
            Status::LowValue => line.bright_black().to_string(),
            _ => line
        };
        println!("{}", colored_line);

        // 将有效的pool添加到StatusManager中
        if data.status == Status::Good || data.status == Status::OddGood || data.status == Status::Fee {
            pools_to_add.push(pool.clone());
        }
    }

    // 批量添加到 StatusManager
    for pool in pools_to_add {
        sm.add(pool);
    }
}


/// 检查MEV路径的费用和状态 - 多线程并发版本
///
/// 该函数实现MEV路径的批量检查，采用多线程并发处理提升性能
///
/// 核心逻辑：
/// 1. 收集所有需要检查的MEV池子（每个池子的所有MEV路径状态相同）
/// 2. 将MEV路径分批，使用多线程并发处理多个批次
/// 3. 每个线程独立处理一个批次的合约调用和结果更新
/// 4. 实现线程安全的进度统计和错误处理机制
/// 5. 使用 buffer_unordered 控制并发数量，避免过度创建线程
///
/// 多线程设计考虑：
/// - 线程安全：使用 DashMap 确保对 pools.mevs 的并发访问安全
/// - 错误隔离：单个批次失败不影响其他批次的处理
/// - 资源控制：通过 MAX_CONCURRENT_TASKS 限制并发线程数量
/// - 进度追踪：使用原子计数器实现线程安全的进度统计
///
/// # 参数
/// * `contract` - 合约实例，用于调用batch_check_mev
/// * `pools` - 池子数据，包含MEV路径信息（使用DashMap保证线程安全）
///
/// # 返回值
/// * `Result<(), DEXError>` - 成功时返回Ok，失败时返回错误
pub async fn check_all_pools_mevs(contract: &Contract, pools: &Pools) -> Result<(), DEXError> {
    println!("{}, pools.data: {}, pools.mev: {}", "开始检查MEV路径...".green(), pools.data.len(), pools.mevs.len());

    // 收集所有需要检查的MEV池子地址（每个池子的所有MEV路径状态相同）
    let mut unchecked_pool = Vec::new();
    for entry in pools.mevs.iter() {
        let pool_addr = *entry.key();
        let mev_list = entry.value();

        for (i, mev) in mev_list.iter().enumerate() {
            if mev.status == MevStatus::Unchecked {
                unchecked_pool.push((pool_addr, i));
            }
        }
    }

    if unchecked_pool.is_empty() {
        println!("没有需要检查的MEV路径");
        pools.display_mev_count();
        pools.display_pools_count();
        return Ok(());
    }

    // 统计总MEV数量用于进度显示
    let total_mevs: usize = unchecked_pool.len();
    println!("找到 {} 个池子，共 {} 个MEV路径需要检查", pools.data.len(), total_mevs);

    // 创建线程安全的统计计数器
    let processed_counter = Arc::new(AtomicUsize::new(0));

    // 将MEV路径分批，准备并发处理
    let batches: Vec<Vec<(Address, usize)>> = unchecked_pool
        .chunks(BATCH_CHECK_MEV_SIZE)
        .map(|chunk| chunk.to_vec())
        .collect();

    let total_batches = batches.len();
    println!("将 {} 个MEV路径分成 {} 个批次进行并发处理，每批最多 {} 个",
        total_mevs, total_batches, BATCH_CHECK_MEV_SIZE);

    // 使用 buffer_unordered 实现并发处理，遵循项目中已有的并发模式
    let futures = iter(batches.into_iter().enumerate().map(|(batch_idx, batch)| {
        let contract_clone = contract;
        let processed_counter_clone = processed_counter.clone();

        async move {
            // 处理单个批次的MEV检查
            match check_mevs_batch(contract, &batch, pools, batch_idx, total_batches).await {
                Ok(success_count) => {
                    // 原子更新统计计数器
                    let current_processed = processed_counter_clone.fetch_add(success_count, Ordering::SeqCst);

                    // 计算并显示进度
                    let new_processed = current_processed + success_count;
                    let percentage = (new_processed as f64 / total_mevs as f64) * 100.0;
                    print!("\r批次 {}/{} 完成，已处理 {}/{} 个MEV路径 ({:.1}%)", batch_idx + 1, total_batches, new_processed, total_mevs, percentage);

                    Ok(success_count)
                }
                Err(e) => {
                    println!("批次 {}/{} 处理失败: {:?}", batch_idx + 1, total_batches, e);
                    Err(e)
                }
            }
        }
    }))
    .buffer_unordered(CONFIG.concurrency.max_task); // 控制并发数量，避免过度创建线程

    // 收集所有并发任务的结果
    let mut successful_batches = 0;
    let mut failed_batches = 0;

    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok(_) => successful_batches += 1,
            Err(_) => failed_batches += 1,
        }
    }

    // 获取最终统计结果
    let final_processed = processed_counter.load(Ordering::SeqCst);

    // 输出最终结果
    println!("{}", format!("MEV路径检查完成！总共处理了 {} 个MEV路径", final_processed).green());
    println!("  - 成功批次: {}/{}", successful_batches, total_batches);
    if failed_batches > 0 {
        println!("  - 失败批次: {} (这些批次的MEV路径未被处理)", failed_batches);
    }
    pools.display_mev_count();
    pools.display_pools_count();
    Ok(())
}




/// 处理一批池子的MEV路径检查 - 并发优化版本
///
/// 该函数是多线程并发处理的核心实现，负责单个批次的MEV检查
/// 相比原版本，增加了批次信息用于更好的日志输出和错误追踪
///
/// # 参数
/// * `contract` - 合约实例（Arc包装，支持多线程共享）
/// * `pool_and_indexs` - 需要检查的池子地址和索引数组
/// * `pools` - 池子数据，使用DashMap保证线程安全
/// * `batch_idx` - 当前批次索引，用于日志输出
/// * `total_batches` - 总批次数量，用于进度显示
///
/// # 返回值
/// * `Result<(usize, usize, usize), DEXError>` - (成功处理数量, 零费用数量, 非零费用数量)
async fn check_mevs_batch(
    contract: &Contract,
    pool_and_indexs: &[(Address, usize)],
    pools: &Pools,
    batch_idx: usize,
    total_batches: usize,
) -> Result<usize, DEXError> {
    // 构建合约请求数据 - 内联简短的构建逻辑
    let mut pool_reqs_batch = Vec::new();
    let mut mev_pool_mapping = Vec::new(); // 记录每个请求对应的池子地址，用于结果处理

    // 构建单个MEV的PoolReq数据
    for (addr, index) in pool_and_indexs {
        if let Some(mevs) = pools.mevs.get(addr) {
            let mev = &mevs[*index];

            let mut mev_req = Vec::new();
            for p in &mev.pools {
                let mev_pool = pools.data.get(&p.addr).unwrap();
                let data = mev_pool.data();
                mev_req.push(ViraData::PoolReq {
                    addr: p.addr,
                    version: U256::from(data.ver),
                    fee: U256::ZERO,  // 费用在合约中计算
                    fp: data.fp,
                    inIndex: U256::from(p.in_index),
                    outIndex: U256::from(p.out_index),
                });
            }

            pool_reqs_batch.push(mev_req);
            mev_pool_mapping.push((addr, index));
        }
    }

    if pool_reqs_batch.is_empty() {
        println!("批次 {}/{}: 警告：没有有效的MEV路径数据可供检查", batch_idx + 1, total_batches);
        return Ok(0);
    }

    //println!("批次 {}/{}: 正在检查 {} 个MEV路径...", batch_idx + 1, total_batches, pool_reqs_batch.len());

    // 尝试批量检查，失败时进行单个重试 - 内联重试逻辑
    let check_results = match contract.batch_check_mev(pool_reqs_batch.clone()).await {
        Ok(results) => results,
        Err(e) => {
            println!("批次 {}/{}: batch_check_mev 失败: {:?}. 返回空结果.", batch_idx + 1, total_batches, e);
            // 返回一个空数组，让后续逻辑知道这个批次失败了
            return Ok(0);
        }
    };

    // 处理检查结果并更新MEV状态 - 修复费用写入逻辑
    let mut processed_count = 0;
    for (i, result) in check_results.iter().enumerate() {
        if i >= mev_pool_mapping.len() {
            break;
        }

        let (addr, index) = mev_pool_mapping[i];

        // 更新对应池子的所有MEV状态（因为同一个池子的所有MEV状态相同）
        // 使用DashMap的线程安全访问
        if let Some(mut mev_list) = pools.mevs.get_mut(addr) {
            let mev = &mut mev_list[*index];
            // 更新gas消耗
            mev.gas = result.gas;

            // 更新正向交易状态 (status0) 和费用
            mev.status = if !result.fee0.is_empty() {
                apply_mev_pool_fees(&mut mev.pools, &result.fee0, true);
                MevStatus::Active
            } else {
                MevStatus::Bad
            };

            // 更新反向交易状态 (status1) 和费用
            mev.status_desc = if !result.fee1.is_empty() {
                apply_mev_pool_fees(&mut mev.pools, &result.fee1, false);
                MevStatus::Active
            } else {
                MevStatus::Bad
            };

            processed_count += 1;
        }
    }

    //println!("批次 {}/{}: 完成处理 {} 个MEV路径", batch_idx + 1, total_batches, processed_count);
    Ok(processed_count)
}

/// 更新MEV路径中各个池子的费用
///
/// 将合约返回的费用数组按顺序写入到MEV路径中对应池子的费用字段
///
/// # 参数
/// * `mev_pools` - MEV路径中的池子列表
/// * `fee_array` - 合约返回的费用数组
/// * `order_asc` - true表示顺序，false逆向
fn apply_mev_pool_fees(mev_pools: &mut [MevPool], fee_array: &[U256], order_asc: bool) {
    // 检查数组长度是否匹配
    if fee_array.len() != mev_pools.len() {
        panic!(
            "⚠️ 费用数组长度不匹配: 期望 {} 个费用，实际收到 {} 个费用",
            mev_pools.len(),
            fee_array.len()
        );
    } else {
        // 长度匹配的正常情况：按顺序更新每个池子的费用
        for (i, fee) in fee_array.iter().enumerate() {
            if order_asc {
                mev_pools[i].fee = *fee;
            } else {
                mev_pools[i].fee_desc = *fee;
            }
        }
    }
}


#[cfg(test)]
mod tests {
    use serde_json::de;

    use super::*;
    use crate::vira::status::mev::{Mev, MevPool};
    use crate::vira::pool::{PoolData, PoolDataToken};
    use crate::vira::token::Token;
    use std::str::FromStr;
    use std::time::Instant;


    /// 创建测试用的池子数据
    fn create_test_pool(addr: &str, ver: u16) -> POOL {
        let mut pool_data = PoolData::default();
        pool_data.addr = Address::from_str(addr).unwrap();
        pool_data.ver = ver;
        pool_data.fp = U256::from(3000); // 0.3% fee

        // 添加两个测试token
        pool_data.tokens = vec![
            PoolDataToken {
                addr: Address::from_str("0x1111111111111111111111111111111111111111").unwrap(),
                index: 0,
                symbol: "TOKEN0".to_string(),
                decimal: 18,
                reserve: U256::from(1000000),
                fee: U256::ZERO,
                weight: U256::ZERO,
            },
            PoolDataToken {
                addr: Address::from_str("0x2222222222222222222222222222222222222222").unwrap(),
                index: 1,
                symbol: "TOKEN1".to_string(),
                decimal: 18,
                reserve: U256::from(2000000),
                fee: U256::ZERO,
                weight: U256::ZERO,
            },
        ];

        // 创建UniV2Pool实例
        use crate::vira::dex::uni_v2::pool::UniV2Pool;
        POOL::UniV2Pool(UniV2Pool { data: pool_data })
    }

    /// 创建测试用的MEV路径
    fn create_test_mev(pool_addrs: Vec<&str>) -> Mev {
        let mut mev = Mev::default();
        mev.s_in = Address::from_str("0x1111111111111111111111111111111111111111").unwrap();
        mev.s_out = Address::from_str("0x2222222222222222222222222222222222222222").unwrap();

        mev.pools = pool_addrs.iter().enumerate().map(|(_i, addr)| {
            MevPool {
                addr: Address::from_str(addr).unwrap(),
                in_index: 0,
                out_index: 1,
                ..Default::default()
            }
        }).collect();

        mev
    }

    #[test]
    fn test_mev_data_structure() {
        // 测试MEV数据结构的创建和基本属性
        let mev = create_test_mev(vec![
            "******************************************",
            "******************************************"
        ]);

        assert_eq!(mev.pools.len(), 2);
        assert_eq!(mev.status, MevStatus::Unchecked);
        assert_eq!(mev.status_desc, MevStatus::Unchecked);
        println!("✅ MEV数据结构测试通过");
    }

    #[test]
    fn test_pools_data_structure() {
        // 测试Pools数据结构
        let pools = Pools::default();

        // 添加测试池子
        let pool1 = create_test_pool("******************************************", 2);
        let pool2 = create_test_pool("******************************************", 2);

        pools.data.insert(pool1.addr(), pool1);
        pools.data.insert(pool2.addr(), pool2);

        // 添加测试MEV
        let mev = create_test_mev(vec![
            "******************************************",
            "******************************************"
        ]);

        let main_pool_addr = Address::from_str("0x5555555555555555555555555555555555555555").unwrap();
        pools.mevs.insert(main_pool_addr, vec![mev]);

        assert_eq!(pools.data.len(), 2);
        assert_eq!(pools.mevs.len(), 1);
        println!("✅ Pools数据结构测试通过");
    }

    /// 模拟check_mevs函数的数据准备逻辑
    #[test]
    fn test_mev_filtering_logic() {
        let pools = Pools::default();

        // 创建混合状态的MEV路径
        let mut mev1 = create_test_mev(vec!["******************************************"]);
        mev1.status = MevStatus::Unchecked; // 需要检查
        mev1.status_desc = MevStatus::Active;    // 已检查

        let mut mev2 = create_test_mev(vec!["******************************************"]);
        mev2.status = MevStatus::Active;    // 已检查
        mev2.status_desc = MevStatus::Active;    // 已检查

        let mut mev3 = create_test_mev(vec!["0x5555555555555555555555555555555555555555"]);
        mev3.status = MevStatus::Unchecked; // 需要检查
        mev3.status_desc = MevStatus::Unchecked; // 需要检查

        let main_pool_addr = Address::from_str("0x6666666666666666666666666666666666666666").unwrap();
        pools.mevs.insert(main_pool_addr, vec![mev1, mev2, mev3]);

        // 模拟过滤逻辑
        let mut unchecked_count = 0;
        for entry in pools.mevs.iter() {
            let mev_list = entry.value();
            for mev in mev_list.iter() {
                if mev.status == MevStatus::Unchecked || mev.status_desc == MevStatus::Unchecked {
                    unchecked_count += 1;
                }
            }
        }

        assert_eq!(unchecked_count, 2); // mev1 和 mev3 需要检查
        println!("✅ MEV过滤逻辑测试通过，找到 {} 个需要检查的MEV", unchecked_count);
    }

    /// 测试 USD 价值过滤功能
    #[test]
    fn test_usd_value_filtering() {
        // 创建测试用的状态管理器
        let mut sm = StatusManager::new();

        // 使用配置中实际存在的稳定币地址
        let stable_addr = Address::from_str("******************************************").unwrap(); // wpls
        sm.tokens.add(Token {
            addr: stable_addr,
            symbol: "WPLS".to_string(),
            decimals: 18, // WPLS 是 18 位小数
            price: 0.00005,  // 配置中的价格
            is_eth: true,
        });

        // 创建测试池子
        let mut high_value_pool = create_test_pool("******************************************", 2);
        let mut low_value_pool = create_test_pool("******************************************", 2);

        // 设置高价值池（稳定币储备量：1000 WPLS，总价值约 $0.1）
        high_value_pool.data_mut().tokens[0].addr = stable_addr;
        high_value_pool.data_mut().tokens[0].symbol = "WPLS".to_string();
        high_value_pool.data_mut().tokens[0].decimal = 18;
        high_value_pool.data_mut().tokens[0].reserve = U256::from(1000u64) * U256::from(10u64).pow(U256::from(18u64)); // 1000 WPLS

        // 设置低价值池（稳定币储备量：0.001 WPLS，总价值约 $0.0000001）
        low_value_pool.data_mut().tokens[0].addr = stable_addr;
        low_value_pool.data_mut().tokens[0].symbol = "WPLS".to_string();
        low_value_pool.data_mut().tokens[0].decimal = 18;
        low_value_pool.data_mut().tokens[0].reserve = U256::from(1u64) * U256::from(10u64).pow(U256::from(15u64)); // 0.001 WPLS

        let stable_pools = vec![high_value_pool, low_value_pool];

        // 执行过滤
        let filtered_pools = filter_low_value_pools(stable_pools);

        // 验证结果：应该只保留高价值池
        assert_eq!(filtered_pools.len(), 1);
        assert_eq!(filtered_pools[0].addr(), Address::from_str("******************************************").unwrap());

        println!("✅ USD 价值过滤测试通过：保留 {} 个高价值池，过滤掉低价值池", filtered_pools.len());
    }

    /// 测试多线程并发处理的数据结构和逻辑
    #[test]
    fn test_concurrent_mev_processing_data_structures() {

        let pools = Pools::default();

        // 创建大量测试MEV数据来模拟并发处理场景
        let num_pools = 100;
        let mevs_per_pool = 5;

        let start_time = Instant::now();

        // 批量创建测试数据
        for pool_idx in 0..num_pools {
            let pool_addr = Address::from_str(&format!("0x{:040x}", pool_idx + 1)).unwrap();

            // 为每个池子创建多个MEV路径
            let mut mev_list = Vec::new();
            for mev_idx in 0..mevs_per_pool {
                let mut mev = create_test_mev(vec![
                    &format!("0x{:040x}", pool_idx * 10 + mev_idx + 1),
                    &format!("0x{:040x}", pool_idx * 10 + mev_idx + 2)
                ]);

                // 设置一些MEV为需要检查的状态
                if (pool_idx + mev_idx) % 3 == 0 {
                    mev.status = MevStatus::Unchecked;
                } else {
                    mev.status = MevStatus::Active;
                }

                mev_list.push(mev);
            }

            pools.mevs.insert(pool_addr, mev_list);
        }

        let data_creation_time = start_time.elapsed();
        println!("✅ 创建 {} 个池子，每个池子 {} 个MEV路径，总计 {} 个MEV，耗时: {:?}",
            num_pools, mevs_per_pool, num_pools * mevs_per_pool, data_creation_time);

        // 模拟并发处理的数据收集逻辑
        let filter_start = Instant::now();
        let mut unchecked_pool = Vec::new();

        for entry in pools.mevs.iter() {
            let pool_addr = *entry.key();
            let mev_list = entry.value();

            for (i, mev) in mev_list.iter().enumerate() {
                if mev.status == MevStatus::Unchecked {
                    unchecked_pool.push((pool_addr, i));
                }
            }
        }

        let filter_time = filter_start.elapsed();
        println!("✅ 过滤出 {} 个需要检查的MEV路径，耗时: {:?}", unchecked_pool.len(), filter_time);

        // 模拟批次分组逻辑
        let batch_start = Instant::now();
        let batches: Vec<Vec<(Address, usize)>> = unchecked_pool
            .chunks(BATCH_CHECK_MEV_SIZE)
            .map(|chunk| chunk.to_vec())
            .collect();

        let batch_time = batch_start.elapsed();
        println!("✅ 将MEV路径分成 {} 个批次，每批最多 {} 个，耗时: {:?}",
            batches.len(), BATCH_CHECK_MEV_SIZE, batch_time);

        // 验证数据结构的线程安全性（DashMap的基本操作）
        let thread_safety_start = Instant::now();
        let expected_unchecked = num_pools * mevs_per_pool / 3; // 大约1/3的MEV需要检查
        assert!(unchecked_pool.len() >= expected_unchecked - 10); // 允许一些误差
        assert!(unchecked_pool.len() <= expected_unchecked + 10);

        let thread_safety_time = thread_safety_start.elapsed();
        println!("✅ 线程安全性验证通过，预期约 {} 个未检查MEV，实际 {} 个，耗时: {:?}",
            expected_unchecked, unchecked_pool.len(), thread_safety_time);

        let total_time = start_time.elapsed();
        println!("✅ 多线程并发处理数据结构测试完成，总耗时: {:?}", total_time);
    }
}
