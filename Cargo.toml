[package]
name = "bera"
version = "0.1.0"
edition = "2024"


[dependencies]
async-trait = "0.1.76"
hex = "0.4.3"
futures = "0.3"

serde = { version = "1.0.204", features = ["derive"] }
serde_json = "1.0.120"
flate2 = "1.1.2"


tokio = { version = "1.36.0", features = ["full"] }
eyre = "0.6.12"
async-stream = "0.3.6"

log = "0.4.17"
fern = {version = "0.6.2", features = ["colored"]}
chrono = "0.4.23"
colored = "2.1.0"
thiserror = "1.0.63"

alloy = { version = "1.0.27", features = ["full"] }
#alloy-dyn-abi = "0.8.21"

url = "2.5.0"
rayon = "1.10"
itertools = "0.13.0"
dashmap = { version = "6.1.0", features = ["serde", "rayon"] }
tokio-stream = "0.1.17"
#rand = "0.9.2"
fastrand = "2.3.0"

tracing = "0.1.41"
tracing-subscriber = { version = "0.3.20", features = ["env-filter"] }


[profile.test]
args = ["--nocapture"]


[features]
parallel = []
