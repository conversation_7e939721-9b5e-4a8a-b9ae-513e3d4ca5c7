// SPDX-License-Identifier: UNLICENSED
import "@openzeppelin/contracts/access/Ownable.sol";

import "./ViraData.sol";
import "./IVira.sol";

//import "forge-std/console.sol";

pragma solidity ^0.8.0;

contract ViraLogic is Ownable {
    using Roles for Roles.Role;
    Roles.Role private _operators;
    address public logicContact;
    mapping(uint256 => address) adapters;

    uint112 constant MAX_UINT112 = type(uint112).max;

    constructor() Ownable(msg.sender) {
        _operators.add(msg.sender);
        _operators.add(address(this));
    }

    modifier onlyOperator() {
        require(_operators.has(msg.sender), "Operators: caller is not the Operator");
        _;
    }

    // adapter begin

    function _getPair(ViraData.PoolReq memory _p) public view returns (ViraData.PoolData memory) {
        if(_p.version == 1 || _p.version == 2 || _p.version == 3 || _p.version == 4){
            //uniswap
            address[] memory tokens = new address[](2);
            tokens[0] = IPoolV2(_p.addr).token0();
            tokens[1] = IPoolV2(_p.addr).token1();

            uint256[] memory reserves = new uint256[](2);
            (reserves[0], reserves[1]) = IPoolV1(_p.addr).getReserves();

            bool preview = _p.version == 1 || _p.version == 2;
            return ViraData.PoolData(_p.addr, _p.version, _p.fee, _p.fp, _p.inIndex, _p.outIndex, false, preview, tokens, reserves);
        } else if(_p.version == 20){
            //bera
            address beraRouter = 0x0d5862FDbdd12490f9b4De54c236cff63B038074;
            (address[] memory tokens, uint[] memory reserves) = IBeraRouter(beraRouter).getLiquidity(_p.addr);
            return ViraData.PoolData({
                addr: _p.addr,
                version: _p.version,
                fee: _p.fee,
                fp: _p.fp,
                inIndex : _p.inIndex,
                outIndex : _p.outIndex, 
                swapByRouter : true,
                preview: false,
                t : tokens,
                r : reserves
            });
        } else if(adapters[_p.version] != address(0)){
            return IAdapter(adapters[_p.version]).getPair(_p); //call
        } else {
            revert('p');
        }
    }

    function _getReserves(ViraData.PoolData memory _p) public returns (uint rIn, uint rOut) {
        if(_p.version == 1 || _p.version == 2 || _p.version == 3 || _p.version == 4){
            (uint r0, uint r1) = IPoolV1(_p.addr).getReserves();
            (rIn, rOut) = _p.inIndex == 0 ? (r0, r1) : (r1, r0);
        } else if(_p.version == 20){
            //bera
            address beraRouter = 0x0d5862FDbdd12490f9b4De54c236cff63B038074;
            (, uint[] memory reserves) = IBeraRouter(beraRouter).getLiquidity(_p.addr);
            rIn = reserves[_p.inIndex];
            rOut = reserves[_p.outIndex];
        } else if(adapters[_p.version] != address(0)){
            return IAdapter(adapters[_p.version]).getReserves(_p); //call
        } else {
            revert('r');
        }
    }

    function _getAmountOut(ViraData.PoolData memory _p, uint amountIn, bool onlyRouterFee) public view returns (uint) {
        if(_p.version == 1 || _p.version == 2){
            //uniswapV1 V2
            uint rIn = _p.r[_p.inIndex];
            uint rOut = _p.r[_p.outIndex];
            return getAmountOutWithFee(amountIn, rIn, rOut, onlyRouterFee ? _p.fp : _p.fp + _p.fee);
        //} else if(_p.version == 3){  
        } else if(_p.version == 20){
            //bera
            address beraRouter = 0x0d5862FDbdd12490f9b4De54c236cff63B038074;
            if(!onlyRouterFee) amountIn = amountIn * (10000 - _p.fee) / 10000;
            (, uint amount) = IBeraRouter(beraRouter).getPreviewSwapExact(IBeraRouter.SwapKind.GIVEN_IN, _p.addr, _p.t[_p.inIndex], amountIn, _p.t[_p.outIndex]);
            return amount;
        } else if(adapters[_p.version] != address(0)){
            return IAdapter(adapters[_p.version]).getAmountOut(_p, amountIn, onlyRouterFee); //call
        } else {
            revert('o');
        }
        // _p.version == 3 || _p.version == 4 需要在线计算
        // if (p[i].version == 22) { //stableV2
        // t.amountOut = IPair(p[i].addr).getAmountOut(t.amountInAfter, tokenIn);
    }

    function getAmountsOutByPool(ViraData.PoolData[] memory ps, uint amountIn) public view returns (uint256[] memory amounts, uint256 amountOut){
        amounts = new uint256[](ps.length+1);
        amounts[0] = amountIn;
        for(uint i = 0; i < ps.length; i++){
            amountIn = _getAmountOut(ps[i], amountIn, false);
            amounts[i+1] = amountIn;
        }
        amountOut = amounts[amounts.length - 1];
    }

    error SwapErr();
    error VerErr();
    error InsufficientOutputError();
    error PoolSwapError(string reason);

    //amountInAfter, 实际进入到pool的token数量
    function _swap(ViraData.PoolData memory p, uint amountInAfter, address to) public returns (uint256 amountOut) {
        if (p.version == 1 || p.version == 2) {
            amountOut = _getAmountOut(p, amountInAfter, true); //preview
            //token过少或者计算方法不对
            if (amountOut == 0) { revert InsufficientOutputError(); }

            (uint256 amount0Out, uint256 amount1Out) = p.inIndex == 0 ? (uint256(0), amountOut) : (amountOut, uint256(0));
            
            if (p.version == 1) {
                try IPoolV1(p.addr).swap(amount0Out, amount1Out, to) {
                    // Swap successful
                } catch Error(string memory reason) {
                    revert PoolSwapError(reason);
                } catch {
                    revert PoolSwapError("ue");
                }
            } else {
                try IPoolV2(p.addr).swap(amount0Out, amount1Out, to, new bytes(0)) {
                    // Swap successful
                } catch Error(string memory reason) {
                    revert PoolSwapError(reason);
                } catch {
                    revert PoolSwapError("ue");
                }
            }
        } else if(p.version == 20){
            //bera
            address beraRouter = 0x0d5862FDbdd12490f9b4De54c236cff63B038074;
            //检查router的授权额度
            address tokenIn = p.t[p.inIndex];
            if (IERC20(tokenIn).allowance(address(this), beraRouter) < amountInAfter) {
                IERC20(tokenIn).approve(beraRouter, amountInAfter);
            }
            (, uint256[] memory amounts) = IBeraRouter(beraRouter).swap(IBeraRouter.SwapKind.GIVEN_IN, p.addr, tokenIn, amountInAfter, p.t[p.outIndex], 0, block.timestamp + 100);
            amountOut = amounts[0];
        } else if (adapters[p.version] != address(0)) {
            //IAdapter(adapters[p.version]).swap(p, t, to); //delegate-call
        } else {
            revert VerErr();
        }
    }

    function _swapPools(ViraData.PoolData[] memory ps, uint amountIn) public returns (address tokenOut, uint256[] memory fees) {
        fees = new uint256[](ps.length);
        
        //如果第一个不走router, 把token发送到pool
        address tokenIn = ps[0].t[ps[0].inIndex];
        if (!ps[0].swapByRouter) IERC20(tokenIn).transfer(ps[0].addr, amountIn);

        uint botReserve; //自己的持仓
        uint amountInBefore = amountIn; //上一次swap到账
        uint amountInAfter = amountIn; //实际到账
        
        for (uint256 i = 0; i < ps.length; i++) {
            if(amountInBefore == 0){
                fees[i] = 900001;
                continue;
            }
            
            ViraData.PoolData memory p = ps[i];
            //有些fee token转账时候会变动pair的r，需要动态获取
            (p.r[p.inIndex], p.r[p.outIndex]) = _getReserves(p);

            tokenIn = p.t[p.inIndex];
            tokenOut = p.t[p.outIndex];

            //计算实际到账，如果是swapByRouter，计算当前bot的差值
            if (p.swapByRouter) {
                if (i > 0) amountInAfter = IERC20(tokenIn).balanceOf(address(this)) - botReserve;
            } else {
                //实际到账
                amountInAfter = IERC20(tokenIn).balanceOf(address(p.addr)) - p.r[p.inIndex];
            }

            //下一个pair是only router, swap前记录amountOut给下一次循环使用
            if (i < ps.length - 1 && ps[i + 1].swapByRouter) botReserve = IERC20(tokenOut).balanceOf(address(this));

            //计算tokenFee
            if (amountInBefore == amountInAfter) {
                fees[i] = 0;
            } else {
                if (amountInBefore < amountInAfter) {
                    //honey pot或者计算错误, 下一个pool获得的token比预期的多
                    fees[i] = 0;
                } else {
                    fees[i] = (amountInBefore - amountInAfter) * 10000 / amountInBefore;
                    if (fees[i] > 1 && fees[i] % 10 != 0) fees[i] = 10 * ((fees[i] / 10) + 1); //向上取整到10的倍数
                }
            }

            //最后一个pair发送给自己
            address to = (i == ps.length - 1 || ps[i + 1].swapByRouter) ? address(this) : ps[i + 1].addr;
            
            //amountInBefore = _swap(p, amountInAfter, to);
        
            try this._swap(p, amountInAfter, to) returns (uint256 _amountOut) {
                amountInBefore = _amountOut;
            } catch {
                revert SwapErr();
            }
        }
        return (tokenOut, fees);
    }

    // adapter end

    // pump begin
    function _pump(ViraData.PumpReq calldata pump) public returns (ViraData.PumpResult status, uint256 reward) {
        ViraData.PoolData[] memory pairs = poolReqToData(pump.pairs);
        
        address tokenIn = pairs[0].t[pairs[0].inIndex];
        uint256 amountIn;
        uint256 balance;
        bool outOfBalance;
        
        //根据amountIn的值，决定使用哪种计算方式
        if (pump.amountIn > 0) {
            amountIn = pump.amountIn;
        } else {
            if (pump.calc == 0) { // 0: 使用 findMaxV2 计算
                amountIn = findMaxV2(pairs);
            } else { // 1,2: 使用 findMaxGolden 计算，3: 跳过预览
                balance = IERC20(tokenIn).balanceOf(address(this));
                if (balance == 0) {
                    return (ViraData.PumpResult.Balance0, 0);
                }
                //一般用于在线计算，建议使用单位是1分钱的token数量
                amountIn = findMaxGolden(amountIn, amountIn / 2, balance * 105 / 100, pairs);
            }
        }

        if (amountIn == 0 || amountIn >= MAX_UINT112) {
            return (ViraData.PumpResult.FindMax0, 0);
        }

        //还未赋值
        if (balance == 0) {
            balance = IERC20(tokenIn).balanceOf(address(this));
            if (balance == 0) return (ViraData.PumpResult.Balance0, 0);
        }

        if (amountIn > balance) {
            amountIn = balance;
            outOfBalance = true;
        }

        address tokenOut = pairs[pairs.length - 1].t[pairs[pairs.length - 1].outIndex];
        
        //0,1,2 有preview
        if(pump.calc != 3){
            (, uint amountOut) = getAmountsOutByPool(pairs, amountIn);
            if (amountIn >= amountOut) {
                //没有利润
                return (ViraData.PumpResult.FindMaxErr, 0);
            } else if (amountIn + pump.cost >= amountOut) {
                //利润小于cost
                return (ViraData.PumpResult.RewardTooLow, 0);
            }
        }

        //执行swap
        if (tokenIn != tokenOut) balance = IERC20(tokenOut).balanceOf(address(this)) + amountIn;
        //v2会多一次getAmoutOut
        try this._swapPools(pairs, amountIn) returns (address, uint256[] memory) {
        } catch {
            revert SwapErr();
        }
        uint256 balanceAfter = IERC20(tokenOut).balanceOf(address(this));

        if (balanceAfter > balance) {
            reward = balanceAfter - balance;
        } else {
            //计算结果实际结果不符合，通常是fee错误
            revert("3");
        }

        //转换weth
        if (pump.convertEth == 1) {
            IERC20(tokenOut).withdraw(reward + amountIn);
            IERC20(tokenIn).deposit{value: address(this).balance}();
        }
        status = outOfBalance ? ViraData.PumpResult.SuccessOverlow : ViraData.PumpResult.Success;
    }

    event PumpResults(uint8[], uint112[]); //提取到其他文件会编译不了
    function pumpSmart(ViraData.PumpReq[] calldata raws) public returns (uint8[] memory status, uint112[] memory rewards)
    {
        status = new uint8[](raws.length);
        rewards = new uint112[](raws.length);

        for (uint256 i = 1; i < raws.length + 1; i++) {
            ViraData.PumpReq memory pump = raws[i - 1];

            //不够gas处理后面的逻辑
            if (gasleft() < pump.gasLimit + 20000) break;

            try this._pump(pump) returns (ViraData.PumpResult _status, uint256 _reward) {
                if (status[i - 1] == 0) status[i - 1] = uint8(_status);
                rewards[i - 1] += uint112(_reward);
                //用尽了流动性，当前池子再跑一次
                if (_status == ViraData.PumpResult.SuccessOverlow) {
                    i = i - 1;
                }
            } catch {
                status[i - 1] = uint8(ViraData.PumpResult.SwapErr);
            }
        }
        emit PumpResults(status, rewards);
    }
    // pump end

    // Tools begin
    function getAmountOutWithFee(uint256 amountIn, uint256 reserveIn, uint256 reserveOut, uint256 fee)
        internal
        pure
        returns (uint256 amountOut)
    {
        uint256 amountInWithFee = amountIn * (10000 - fee);
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = (reserveIn * 10000) + amountInWithFee;
        amountOut = numerator / denominator;
    }
    function sqrt(uint256 y) internal pure returns (uint256 z) {
        // babylonian method
        if (y > 3) {
            z = y;
            uint256 x = y / 2 + 1;
            while (x < z) {
                z = x;
                x = (y / x + x) / 2;
            }
        } else if (y != 0) {
            z = 1;
        }
    }

    function findMaxV2(ViraData.PoolData[] memory p) public pure returns (uint256) {
        if (p.length == 0) {
            return 0;
        }

        // Initialize virtual pool with the first pool in the path
        ViraData.PoolData memory d = p[0];
        if (d.r[d.inIndex] == 0 || d.r[d.outIndex] == 0) {
            return 0;
        }
        
        uint256 Ea = d.r[d.inIndex];
        uint256 Eb = d.r[d.outIndex];
        uint256 Efee = 10000 - d.fp - d.fee;

        // Iteratively combine the rest of the pools
        unchecked {
            for (uint256 i = 1; i < p.length; i++) {
                d = p[i];
                uint256 reserve_in = d.r[d.inIndex];
                uint256 reserve_out = d.r[d.outIndex];

                if (reserve_in == 0 || reserve_out == 0) {
                    return 0;
                }

                uint256 fee = 10000 - d.fp - d.fee;
                
                uint256 denominator = reserve_in + (Eb * fee / 10000);
                if (denominator == 0) {
                    return 0;
                }

                Ea = Ea * reserve_in / denominator;
                Eb = Eb * reserve_out * fee / 10000 / denominator;
            }

            // Calculate the optimal input for the final aggregated virtual pool
            if (Ea == 0 || Eb == 0 || Efee == 0) {
                return 0;
            }
            
            uint256 sqrt_part = Ea * Eb * Efee / 10000;
            if (sqrt_part == 0) {
                return 0;
            }
            
            uint256 sqrt_val = sqrt(sqrt_part);
            if (sqrt_val <= Ea) {
                return 0; // No profit
            }

            uint256 optimalAmount = (sqrt_val - Ea) * 10000 / Efee;
            return optimalAmount >= MAX_UINT112 ? 0 : optimalAmount;
        }
    }

    function findMaxGolden(uint256 low, uint256 tolerance, uint256 high, ViraData.PoolData[] memory ps) internal view returns (uint256) {
        uint8 step;
        uint8 stepMax = 70;

        (, uint256 amountOutx1) = getAmountsOutByPool(ps, low);
        if (amountOutx1 < low) return 0;
        uint256 max = type(uint112).max; //MAX_UINT112
        uint256 x1 = high - ((high - low) * 1e6) / 1618034;
        uint256 x2 = low + ((high - low) * 1e6) / 1618034;

        (, amountOutx1) = getAmountsOutByPool(ps, x1);
        (, uint256 amountOutx2) = getAmountsOutByPool(ps, x2);

        uint256 f1 = amountOutx1 + max - x1; //确保不溢出
        uint256 f2 = amountOutx2 + max - x2; //确保不溢出

        while (high > tolerance + low || step < stepMax) {
            //超过32步就直接返回
            if (f1 > f2) {
                high = x2;
                x2 = x1;
                f2 = f1;
                x1 = high - ((high - low) * 1e6) / 1618034;
                (, amountOutx1) = getAmountsOutByPool(ps, x1);
                f1 = amountOutx1 + max - x1; //确保不溢出
            } else if (f1 < f2) {
                low = x1;
                x1 = x2;
                f1 = f2;
                x2 = low + ((high - low) * 1e6) / 1618034;
                (, amountOutx2) = getAmountsOutByPool(ps, x2);
                f2 = amountOutx2 + max - x2; //确保不溢出
            } else {
                low = x1;
                high = x2;
                x1 = high - ((high - low) * 1e6) / 1618034;
                x2 = low + ((high - low) * 1e6) / 1618034;
                (, amountOutx1) = getAmountsOutByPool(ps, x1);
                (, amountOutx2) = getAmountsOutByPool(ps, x2);
                f1 = amountOutx1 + max - x1; //确保不溢出
                f2 = amountOutx2 + max - x2; //确保不溢出
            }
            step++;
        }
        return (high + low) / 2;
    }

    function poolReqToData(ViraData.PoolReq[] calldata p) internal view returns (ViraData.PoolData[] memory) {
        ViraData.PoolData[] memory ps = new ViraData.PoolData[](p.length);
        for (uint256 i = 0; i < p.length; i++) {
            ps[i] = _getPair(p[i]);
        }
        return ps;
    }
    // Tools end


    // TEST BEGIN
    struct CheckPairFeeInputDesc {
        ViraData.PoolReq [] pair; //只使用inIndex
        ViraData.PoolReq [] prePair; //前置pair, 在没有stable token的情况下使用
    }

    function _getInitialAmount(address token, address[] calldata stables, uint[] calldata stableAmountIns) 
        private view returns (uint256) {
        for (uint256 i = 0; i < stables.length; i++) {
            if (stables[i] == token) {
                return stableAmountIns[i];
            }
        }
        return IERC20(token).balanceOf(address(this)) / 2;
    }

    /**
     * @notice 批量检查交易对的手续费
     * @param input 输入参数数组，每个元素包含:
     *        - pair: 目标交易对数组，使用inIndex指定输入token的位置
     *        - prePair: 前置交易对数组，用于没有稳定币的情况
     * @return fees 二维数组，对应每个输入的手续费结果:
     *         - 正常情况: 返回实际手续费数值
               - 10000 : 100% fee
               - 666666: * pair获得的token比转入的还多, honey token
               - 900000: 转账到Pair失败，不能卖
               - 900001: swapErr, router fee不对或者token太少
               - 900002: router fee不对, 或者pool剩余数量十分少
               - 900003: swap出来的另外一个代币不存在自己的账号上
               - 900004: * 通缩异常，transfer后pool的reserve减少了
               - 900005: batchCheckPairFee失败，未知异常
     */
    function batchCheckPairFee(CheckPairFeeInputDesc[] calldata input, address[] calldata stables, uint[] calldata stableAmountIns)
        public
        returns (uint[][] memory fees)
    {
        fees = new uint[][](input.length);

        for (uint256 z; z < input.length; z++) {
            address tokenIn;
            uint256 amountIn;

            ViraData.PoolData[] memory pairs = poolReqToData(input[z].pair);
            uint[] memory fee = new uint[](pairs[0].t.length);

            // 处理需要前置swap的情况
            if (input[z].prePair.length > 0) {
                ViraData.PoolData[] memory pre_pairs = poolReqToData(input[z].prePair);
                tokenIn = pre_pairs[0].t[pre_pairs[0].inIndex];
                
                // 获取初始金额
                amountIn = _getInitialAmount(tokenIn, stables, stableAmountIns);
                
                // 执行前置swap
                try this._swapPools(pre_pairs, amountIn) returns (address _tokenOut, uint256[] memory) {
                    tokenIn = _tokenOut;
                    amountIn = IERC20(tokenIn).balanceOf(address(this));
                } catch {
                    fee[0] = 900001; // prePair的fee不正确或者前置pair的token过少
                    fees[z] = fee;
                    continue;
                }
            } else {
                tokenIn = pairs[0].t[pairs[0].inIndex];
                amountIn = _getInitialAmount(tokenIn, stables, stableAmountIns);
            }


            //stable token数量不足, 可能是多次swap导致剩余stable为0
            require(amountIn > 0, "N Stable");
            
            //把tokenIn分成n份，swap后持有pool的所有token
            amountIn = amountIn / pairs[0].t.length;
            for (uint256 i = 0; i < pairs[0].t.length; i++) {
                // 如果是稳定币，直接设置fee为0并跳过
                for (uint256 j = 0; j < stables.length; j++) {
                    if (stables[j] == pairs[0].t[i]) {
                        fee[i] = 0;
                        continue;
                    }
                }
                address tokenOut;
                uint amountOut;
                if(pairs[0].t[i] == tokenIn) {
                    tokenOut = tokenIn;
                    amountOut = amountIn;
                } else {
                    //把其中一份换成需要检测的token
                    pairs[0].outIndex = i;
                    try this._swapPools(pairs, amountIn) returns (address _tokenOut, uint256[] memory) {
                        tokenOut = _tokenOut;
                    } catch {
                        //router fee不对或者pool的token数量太少导致swap出错
                        fee[i] = 900002;
                        fees[z] = fee;
                        continue;
                    }
                    amountOut = IERC20(tokenOut).balanceOf(address(this));
                }
                
                if (amountOut == 0) {
                    //swap出来的另外一个代币不存在自己的账号上, bad fee
                    fee[i] = 900003;
                } else {
                    //把所有token转入到pool, 对比pool的reserve和转入后的数量, 计算fee
                    try IERC20(tokenOut).transfer(pairs[0].addr, amountOut) {
                        (uint rIn, uint rOut) = _getReserves(pairs[0]);
                        uint r = rOut;
                        if(pairs[0].t[i] == tokenIn) {r = rIn;}

                        uint rAfter = IERC20(tokenOut).balanceOf(pairs[0].addr);
                        if (rAfter < r) {
                            //转入token后，pool的reserve减少了, 通缩币
                            fee[i] = 900004;
                        } else if (rAfter == r) {
                            //100% fee
                            fee[i] = 10000;
                        } else {
                            if ((rAfter - r) > amountOut) {
                                fee[i] = 666666; //pair获得的token比转入的还多, honey token
                            } else if((rAfter - r) == amountOut){
                                fee[i] = 0; //没有fee
                            } else {
                                uint f = (amountOut - (rAfter - r)) * 10000 / amountOut;
                                if (f > 1 && f % 10 != 0) f = 10 * ((f / 10) + 1); //如果不是整数向上取整到10的倍数
                                fee[i] = f;
                            }
                        }
                    } catch {
                        //转账到Pair失败，不能卖
                        fee[i] = 900000;
                    }
                }
            }

            fees[z] = fee;
        }
        return fees;
    }

    struct CheckMevsResultDesc {
        uint256 gas; //正反的和取最大值
        uint256[] fee0; //正向gas, 如果是空数组则不可交易
        uint256[] fee1; //反向gas, 如果是空数组则不可交易
    }

    struct MevCheckContext {
        uint256 gasLeft;
        ViraData.PoolData[] pairs;
        uint256[] fees;
        address tokenIn;
        uint256 amountIn;
    }

    function batchCheckMev(ViraData.PoolReq[][] calldata ds, address[] calldata stables, uint[] calldata stableAmountIns) public returns (CheckMevsResultDesc[] memory results) {
        results = new CheckMevsResultDesc[](ds.length);
        for (uint256 i; i < ds.length; i++) {
            MevCheckContext memory ctx;
            //result0, result1, gas0, gas1
            CheckMevsResultDesc memory result;

            //////////////////
            //1.正向操作
            /////////////////
            //先缓存pair信息，避免写入合约缓存的gas消耗影响gas计算结果
            ctx.pairs = poolReqToData(ds[i]);
            ctx.gasLeft = gasleft();
            ctx.tokenIn = ctx.pairs[0].t[ctx.pairs[0].inIndex];

            //使用预设的amountIn， 避免单向mev耗尽货币
            ctx.amountIn = _getInitialAmount(ctx.tokenIn, stables, stableAmountIns);
            try this._swapPools(ctx.pairs, ctx.amountIn) returns (address, uint256[] memory _fee) {
                result.fee0 = _fee;
            } catch {
                //正向交易失败了 fee0的长度是0
            }
            result.gas = ctx.gasLeft - gasleft(); //取最大的gas
            ctx.gasLeft = gasleft();

            /////////////////
            //2.反向操作
            /////////////////
            ctx.tokenIn = ctx.pairs[ctx.pairs.length - 1].t[ctx.pairs[ctx.pairs.length - 1].outIndex];
            ctx.amountIn = _getInitialAmount(ctx.tokenIn, stables, stableAmountIns);
            ctx.pairs = new ViraData.PoolData[](ds[i].length);
            for (uint256 j = 0; j < ds[i].length; j++) {
                // 计算反向索引
                uint256 reverseIndex = ds[i].length - 1 - j;
                // 直接调用 _getPair 并内联创建 PoolReq
                ctx.pairs[j] = _getPair(ViraData.PoolReq({
                    addr: ds[i][reverseIndex].addr,
                    version: ds[i][reverseIndex].version,
                    fee: ds[i][reverseIndex].fee,
                    fp: ds[i][reverseIndex].fp,
                    inIndex: ds[i][reverseIndex].outIndex,
                    outIndex: ds[i][reverseIndex].inIndex
                }));
            }

            try this._swapPools(ctx.pairs, ctx.amountIn) returns (address, uint256[] memory fee) {
                result.fee1 = fee;
            } catch {
                //逆向交易失败了, fee1的长度是0
            }

            //如果逆序的gas比正序的大，则使用逆序的gas
            if (ctx.gasLeft - gasleft() > result.gas) result.gas = ctx.gasLeft - gasleft();

            results[i] = result;
        }
        return results;
    }
    // TEST ENDW



    function _delegate(address implementation) public payable onlyOperator {
        require(implementation != address(0), "el");
        assembly {
            calldatacopy(0, 0, calldatasize())
            let result := delegatecall(gas(), implementation, 0, calldatasize(), 0, 0)
            returndatacopy(0, 0, returndatasize())

            switch result
            case 0 { revert(0, returndatasize()) }
            default { return(0, returndatasize()) }
        }
    }

    //function destroyEx() public onlyOwner { selfdestruct(payable(msg.sender)); }

    receive() external payable {}

    fallback() external payable {
        _delegate(logicContact);
    }
}
